{"name": "magento/magento-cloud-template", "description": "eCommerce Platform for Growth (Enterprise Edition)", "type": "project", "version": "2.4.7-p4", "license": "OSL-3.0", "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "require": {"afterpay-global/module-afterpay": "^5.4", "aheadworks/module-advanced-reports": "^2.11", "amasty/label": "^2.5", "amasty/module-banners-lite": "^1.2", "amasty/module-mage-2.4.5-fix": "^1.0", "amasty/module-one-step-checkout-pro-package": "^2.6", "amasty/module-optimizer-pro": "^3.5", "amasty/module-promo-banners": "^1.15", "amasty/module-shop-by-brand-graphql": "^1.1", "amasty/module-special-promo-pro": "^2.16", "amasty/module-visual-merchandiser": "2.1.3", "amasty/product-labels-graphql": "^1.0", "amasty/promo": "^2.16", "amasty/rgrid": "^1.0", "amasty/shopby": "^3.2", "cweagans/composer-patches": "^1.7", "emartech/emarsys-magento2-extension": "^2.0", "fastly/magento2": "^1.2", "goodby/csv": "^1.3", "handcraftedinthealps/goodby-csv": "^1.4", "humm/module-humm-payment-gateway": "^8.0", "ideatarmac/openpay": "^1.0", "magedelight/megamenu": "2.0.25", "magefan/module-blog": "^2.12", "magefan/module-blog-plus": "^2.12", "magento/composer-root-update-plugin": "~2.0", "magento/ece-tools": "^2002.1", "magento/extension-b2b": "^1.4", "magento/magento-cloud-metapackage": ">=2.4.7 <2.4.8", "magento/product-enterprise-edition": "2.4.7-p6", "magento/pwa": "^0.7", "magento/quality-patches": "^1.1", "magento/security-package": "^1.1", "mageworx/module-seoall": "^2.10", "mageworx/module-seomarkup": "^2.8", "mageworx/module-seosuiteultimate": "2.39.1", "meta/meta-for-magento2": "^1.3", "olegkoval/magento2-regenerate-url-rewrites": "^1.6", "opy/module-payment": "^1.2", "opy/widgets": "^1.1", "paypal/module-braintree": "4.6.1-p5", "phpoffice/phpspreadsheet": "^2.1", "ramsey/uuid": "^4.7.6", "shippit/magento2": "^1.10", "signifyd/module-connect": "^5.8.2", "symfony/http-client": "^7.2", "true/punycode": "^2.1", "unbxd/magento2-product-feed": "2.0.33", "wyomind/googlecustomerreviews": "^6.0", "wyomind/simplegoogleshopping": "^17.0", "xtento/orderexport": "^2.17", "xtento/productexport": "^2.16", "zip/magento2": "^1.2"}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "friendsofphp/php-cs-fixer": "^3.22", "lusitanian/oauth": "~0.8.10", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.7", "pdepend/pdepend": "~2.10.0", "phpcompatibility/php-compatibility": "^9.3", "phpmd/phpmd": "^2.9.1", "phpstan/phpstan": "~1.2.0", "phpunit/phpunit": "^9.5", "sebastian/phpcpd": "^6.0", "squizlabs/php_codesniffer": "~3.6.0", "symfony/process": "*"}, "conflict": {"gene/bluefoot": "*"}, "autoload": {"psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": ["app/code/", "generated/code/"]}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"balance": {"type": "composer", "url": "https://satis.balancenet.com.au"}, "0": {"type": "composer", "url": "https://repo.magento.com/", "canonical": false}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/enterprise/"}, "amasty1": {"type": "composer", "url": "https://composer1.amasty.com/enterprise/"}, "xtento": {"type": "composer", "url": "https://repo.xtento.com"}, "xtento123": {"type": "composer", "url": "https://alt123.repo.xtento.com"}, "aheadworks": {"type": "composer", "url": "https://dist.aheadworks.com/"}, "mageworx_packages": {"type": "composer", "url": "https://packages.mageworx.com/", "canonical": false}, "wyomind": {"type": "composer", "url": "https://:@repo.wyomind.com"}, "magefan": {"type": "composer", "url": "https://magefan.com/repo/"}}, "extra": {"magento-force": true, "magento-deploystrategy": "copy", "component_paths": {"trentrichardson/jquery-timepicker-addon": "lib/web/jquery/jquery-ui-timepicker-addon.js", "components/jquery": ["lib/web/jquery.js", "lib/web/jquery/jquery.min.js", "lib/web/jquery/jquery-migrate.js"], "blueimp/jquery-file-upload": "lib/web/jquery/fileUploader", "components/jqueryui": ["lib/web/jquery/jquery-ui.js"], "twbs/bootstrap": ["lib/web/jquery/jquery.tabs.js"], "tinymce/tinymce": "lib/web/tiny_mce_4"}, "patches": {"amasty/label": {"TOT0024-11 Amasty Label Fix Setup ConvertFlatLabelDataToStructuredView return type": "patches/TOT0024-11-Amasty-Label.patch"}, "magento/framework": {"Fix: Magestore Store Locator module SQLi vulnerability": "patches/magestore_store_locator_sqli.patch", "Fix: Max number of cookies exceeded fix": "patches/TOT0000-3765_max_number_of_cookies_exceeded_2.4.7-p5.patch"}, "magento/module-checkout-agreements": {"Don't check agreements if zipmoney": "patches/Magento-CheckoutAgreements-Model-Checkout-Plugin.patch"}, "zip/magento2": {"Add log data after saved order status for zipmoney": "patches/TOT0000-2047-Add-log-after-save-order-v2.patch", "TOT0021-102: Zippay checkout error with reward point amount": "patches/TOT0021-102_zippay_checkout_error.patch"}, "shippit/magento2": {"Fix: TOT0017-1428 Shipping order not automatically transfered to Shippit": "patches/TOT0017-1428-add-condition-for-scheduled-sync.patch", "Fix: TOT0017-1567 Add Api Key Column": "patches/TOT0017-1567-Shippi_Add_Api_Key_Column.patch", "Fix: TOT0000-2207 Fixed issue in webhook controller": "patches/TOT0000-2207-resolved-webhook-controller-issue.patch", "Fix: TOT0022-26 Fix Monolog Logger": "patches/TOT0022-25_shippit_logger_fix.patch", "TOT9999-1246: Updated hardcode wwwshippitdotcom": "patches/TOT9999-1246-updated-hardcode-shippitdotcom.patch", "TOT9999-158: store api fixed ": "patches/TOT9999-158-shippit-storeapikey-fixed.patch", "TOT9999-879: resolved webhook store api issue": "patches/TOT9999-879-resolved-webhook-store-api-issue.patch", "TOT9999-1110: Added uber shipping information label": "patches/TOT9999-1110-added-uber-shipping-label.patch"}, "magento/module-negotiable-quote-shared-catalog": {"Fix: TOT0017-1172 issue with empty customer group ID": "patches/TOT0017-1227_Error_with_empty_customer_group_id.patch"}, "magento/module-requisition-list": {"Fix: TOT0017-1426 Fixed My Requisition List page": "patches/TOT0017-1426_My-Standard-Order.patch", "Fixed Requisition List page": "patches/TOT0000-3753-Fixed-Requisition-List-page.patch"}, "magento/module-new-relic-reporting": {"Fix: TOT0017-1371_patch on new relic": "patches/TOT0017-1371_patch_on_new-relic.patch"}, "magento/module-tax": {"TOT0021-90: GST calculation reward": "patches/TOT0021-90_GST_calculation_reward.patch"}, "magento/module-quote": {"TOT0021-90: GST calculation quote item": "patches/TOT0021-90_GST_calculation_item.patch"}, "magento/module-target-rule": {"TOT0000-2751: Fixed issue related to target rule in prod": "patches/TOT0000-2751-target-rule-log-issue-fixed.patch"}, "aheadworks/module-advanced-reports": {"TOT0023: Update createdAt filter to 1 year": "patches/TOT0023-92-Aheadworks-Change-CreatedAt-Filter-v2.patch", "TOT0024-11: Fixed deprecated functionality": "patches/TOT0024-11-Fixed-aheadworks.patch", "TOT0000-3397: Disable crons:": "patches/TOT0000-3397-Disable-Aheadworks-Advanced-Reports-Crons.patch"}, "magento/module-catalog": {"TOT0024-11: Fixed getMetaTitle null issue": "patches/TOT0024-11-Fixed-metaTitle-issue.patch", "TOT9999-730: View all products": "patches/TOT9999-730-view-all-products.patch"}, "signifyd/signifyd-php": {"TOT0023: Disable Signifyd logging": "patches/TOT0023_Remove_Signifyd_Logging.patch", "TOT0024-11: Fixed Signifyd Error hnalding": "patches/TOT0024-11-Fixed-Signifyd.patch", "TOT9999-1123: Fixed signifyd php deprecated functionality": "patches/TOT9999-1123-fixed-signifyd-php-deprecated-functionality.patch"}, "amasty/shopby": {"TOT9999-158: Fixed amasty shopby tags setheader issue ": "patches/TOT9999-158-Fixed-shopby-tags-issue.patch", "TOT9999-158: Resolved category ajax request error ": "patches/TOT9999-158-Ajax-navigation-error.patch", "TOT9999-158: Fixed Ternary operator ": "patches/TOT9999-158-fixed-ternary-operator.patch", "TOT9999-875: Fixed category filter layout ajax  ": "patches/TOT9999-875-fixed-category-filter-layout.patch", "TOT9999-1037: Fixed category layout update issue": "patches/TOT9999-1037-fix-category-layout-update-issue.patch"}, "fastly/magento2": {"TOT0024-11: Fixed fastly string replace issue": "patches/TOT0024-11-Fixed-fastly-issue.patch"}, "xtento/productexport": {"TOT0024-11: Fix xtento strlen null issue": "patches/TOT0024-11-xtento-strlen-null.patch"}, "magento/module-price-permissions": {"TOT0024-11: Fixed stripos null issue": "patches/TOT0024-11-Fixed-stripos-null.patch"}, "magento/module-integration": {"TMP disable nonce checkk": "patches/TMP_disable_nonce_check.patch"}, "magento/module-checkout": {"TOT0000-3267: Fixed Carrier with such method not found": "patches/TOT0000-3267-Fixed-carrier-issue.patch"}, "ideatarmac/openpay": {"TOT0000-3251: Enable openpay log to get request and response": "patches/TOT0000-3251-openpay-log.patch", "TOT0000-3251: Enable openpay use logger interface on writing exception": "patches/TOT0000-3251-openpay-logging-issue.patch"}, "magento/module-elasticsearch": {}, "magento/module-customer": {"TOT9999-463: Added business rewards email template": "patches/TOT9999-442-Trade-Rewards-Email-Attributes.patch"}, "magedelight/megamenu": {"TOT0000-3383: 3": "patches/magedelight/BAL0113-893-Fixed_preview_error_upgrade_fix.patch", "TOT0000-3383: 4": "patches/magedelight/BAL0113-894_fixed_category_schedule.patch", "TOT0000-3383: 5": "patches/magedelight/MTL0000-megamenu_static_assets_2.0.13.patch"}, "afterpay-global/module-afterpay": {"TOT9999-494: Fixed afterpay refund issue with redemption": "patches/TOT9999-494-Afterpay-Refund-Issue-With-Redemption-upgrade-fix.patch"}, "mageworx/module-seobase": {"TOT9999-492: Product saving validation exception fix": "patches/TOT9999-492-validation-exception-fix.patch"}, "magento/module-email": {"TOT9999-659: Added extra email template varible for b2b customer": "patches/TOT9999-659-added-extra-email-template-parameter-for-b2b-customer.patch"}, "opy/widgets": {"TOT9999-744: fix undecleard varible opy widget 2.4.7": "patches/TOT9999-744-fix-undecleard-varible-open-pay.patch"}, "paypal/module-braintree-core": {"TOT0000-3571: Braintree Paypal method disappearance fix": "patches/TOT0000-3571-fix-braintree-paypal-disappearance.patch", "TOT9999-906: fix checkout braintree exception": "patches/TOT9999-754-fix-braintree-checkout-error.patch", "TOT9999-1050:Fix paypal line item issue BUNDLE-3429-paypal-1": "patches/BUNDLE-3429-paypal-module-braintree-core-fix-v2.4.7-p3-p4-p5.patch"}, "paypal/module-braintree-customer-balance": {"TOT9999-1050:Fix paypal line item issue BUNDLE-3429-paypal-2": "patches/BUNDLE-3429-paypal-module-braintree-customer-balance-fix-v2.4.7-p3-p4-p5.patch"}, "paypal/module-braintree-gift-card-account": {"TOT9999-1050:Fix paypal line item issue BUNDLE-3429-paypal-3": "patches/BUNDLE-3429-paypal-module-braintree-gift-card-account-fix-v2.4.7-p3-p4-p5.patch"}, "paypal/module-braintree-gift-wrapping": {"TOT9999-1050:Fix paypal line item issue BUNDLE-3429-paypal-4": "patches/BUNDLE-3429-paypal-module-braintree-gift-wrapping-fix-v2.4.7-p3-p4-p5.patch"}, "humm/module-humm-payment-gateway": {"TOT9999-891: fix depricated variables humm": "patches/TOT9999-891-fixed-depricated-vars-hummpay.patch"}, "opy/module-payment": {"TOT9999-891: fix depricated variables opy": "patches/TOT9999-891-fixed-depricated-vars-opy.patch"}, "amasty/module-visual-merchandiser": {"TOT9999-872: Changed indexing order for catalog search": "patches/TOT9999-872-changed-indexing-order.patch"}, "amasty/module-one-step-checkout-core": {"TOT9999-877: Quote null shipping method fix": "patches/TOT9999-877-Null-Object-Fix.patch"}, "magento/module-catalog-graph-ql": {"TOT9999-910: graphql module error fix": "patches/ACPT-1876__attribute_reader_should_use_factory_for_collection__2.4.7.patch"}, "magento/magento-cloud-patches": {"TOT9999-910: graphql module patch moved to custom patches": "patches/TOT9999-910-graph-ql-default-composer-entry-moved.patch"}, "signifyd/module-connect": {"TOT9999-1195: Fixed Signifyd case review issue": "patches/TOT9999-1195-fixed-signifyd-case-review-issue.patch", "TOT9999-956: Signifyd cancel order status to pending": "patches/TOT9999-956-signifyd-cancel-order-state-pending.patch", "TOT9999-936: Fixed Signifyd Logs Error": "patches/TOT9999-936-fixed-signifyd-logs-issue.patch"}, "magento/module-company": {"TOT0000-3707 fix issue customer company": "patches/TOT0000-3707-fix-issue-customer-company.patch", "TOT0000-3707 customer role info": "patches/TOT0000-3707-Customer-RoleInfo.patch"}, "magento/module-sales": {"TOT9999-890: Update order status to pending before cancel": "patches/TOT9999-890-update-order-state-to-pending-before-cancel.patch", "TOT9999-890: Change order state to pending in authorize amount": "patches/TOT9999-890-change-order-state-to-pending-in-authorize-amount.patch"}, "unbxd/magento2-product-feed": {"TOT9999-1027: Added hover image in unbxd": "patches/TOT9999-1027-added-hover-image.patch"}}}}