<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Customer\Block\Form\Login $block */
/** @var \Magento\Customer\ViewModel\LoginButton $loginButtonViewModel */
$loginButtonViewModel = $block->getData('login_button_view_model');
?>

<div class="block block-customer-login">
    <div class="block-title">
        <strong id="block-customer-login-heading" role="heading" aria-level="2"><?= $block->escapeHtml(__('Rewards member login')) ?></strong>
                <p class="required-note"><?php echo __('Mandatory fields'); ?></p>
    </div>
    <div class="block-content" aria-labelledby="block-customer-login-heading">
        <form class="form form-login"
              action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>"
              method="post"
              id="login-form"
              data-mage-init='{"validation":{}}'>
            <?= $block->getBlockHtml('formkey') ?>
            <fieldset class="fieldset login" data-hasrequired="<?= $block->escapeHtml(__('* Required Fields')) ?>">
                <div class="field note customer_login_top_text">
                <?= $this->getLayout()
                        ->createBlock('Magento\Cms\Block\Block')
                        ->setBlockId('customer_login_top_text')
                        ->toHtml();
                    ?>
                 </div>
                <div class="field email required">
                    <label class="label" for="email"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
                    <div class="control">
                        <input name="login[username]" value="<?= $block->escapeHtmlAttr($block->getUsername()) ?>"
                            <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                               id="email" type="email" class="input-text"
                               title="<?= $block->escapeHtmlAttr(__('Email')) ?>"
                               data-mage-init='{"mage/trim-input":{}}'
                               data-validate="{required:true, 'validate-email':true}"
                               placeholder="<?= $block->escapeHtml( __('Enter your email address')); ?>">
                    </div>
                </div>
                <div class="field password required">
                    <label for="pass" class="label"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
                    <div class="control">
                        <input name="login[password]" type="password"
                            <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                               class="input-text" id="pass"
                               title="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                               data-validate="{required:true}"
                               placeholder="<?= $block->escapeHtml( __('Enter your account password')); ?>">
                    </div>
                </div>

                <?php
                $otpHelper = $this->helper('Totaltools\OtpLogin\Helper\Data');
                $isOtpEnabled = $otpHelper->isOtpLoginEnabled();
                ?>

                <?php if ($isOtpEnabled): ?>
                <!-- Login Method Toggle -->
                    <div class="field choice login-method-toggle">
                        <input type="radio"
                            name="login_method"
                            id="login_password"
                            value="password"
                            class="radio"
                            checked="checked" />
                        <label class="label" for="login_password">
                            <span><?= $escaper->escapeHtml(__('Login with Password')) ?></span>
                        </label>
                    </div>
                    <div class="field choice login-method-toggle">
                        <input type="radio"
                            name="login_method"
                            id="login_otp"
                            value="otp"
                            class="radio" />
                        <label class="label" for="login_otp">
                            <span><?= $escaper->escapeHtml(__('Login with OTP')) ?></span>
                        </label>
                    </div>

                 <!-- OTP Method Selection (Hidden by Default) -->
                <div class="field choice otp-method-selection" id="otp-method-field" style="display: none;">
                    <label class="label"><?= $escaper->escapeHtml(__('Send OTP via:')) ?></label>
                    <div class="control">
                        <input type="radio"
                               name="otp_channel"
                               id="otp_email"
                               value="email"
                               class="radio"
                               checked="checked" />
                        <label class="label" for="otp_email">
                            <span><?= $escaper->escapeHtml(__('Email')) ?></span>
                        </label>

                        <input type="radio"
                               name="otp_channel"
                               id="otp_sms"
                               value="sms"
                               class="radio" />
                        <label class="label" for="otp_sms">
                            <span><?= $escaper->escapeHtml(__('SMS')) ?></span>
                        </label>
                    </div>
                </div>
                <?php endif; ?>

                <?= $block->getChildHtml('form_additional_info') ?>
                <div class="actions-toolbar">
                    <div class="primary">
                        <button type="submit" class="action login primary" name="send" id="send2" <?php if ($block->getButtonLockManager()->isDisabled('customer_login_form_submit')): ?> disabled="disabled" <?php endif; ?>><span><?= $block->escapeHtml(__('Login')) ?></span></button>
                        <?php if ($isOtpEnabled): ?>
                             <!-- Send OTP Button (Hidden by Default) -->
                        <button type="button"
                                class="action login primary"
                                id="send-otp-btn"
                                style="display: none;">
                            <span><?= $escaper->escapeHtml(__('Send OTP')) ?></span>
                        </button>
                        <?php endif; ?>
                    </div>
                    <div class="secondary"><a class="action remind" href="<?= $block->escapeUrl($block->getForgotPasswordUrl()) ?>"><span><?= $block->escapeHtml(__('Forgot Your Password?')) ?></span></a></div>

                </div>
            </fieldset>
        </form>
          <!-- Loading Indicator -->
        <div id="otp-loading" class="otp-loading" style="display: none;">
            <div class="loading-spinner"></div>
            <p><?= $escaper->escapeHtml(__('Sending OTP...')) ?></p>
        </div>

        <!-- Messages Container -->
        <div id="otp-messages" class="messages"></div>
    </div>
    <div class="block-bottom">
        <h4>Already a rewards member?</h4>
        <p>Signed up in store but don't have an online account? Create one here.</p>
        <a class="online-register-btn" href="<?= $block->getUrl('customer/account/register'); ?>">Online Register</a>
    </div>
    <?php // phpcs:ignore Magento2.Legacy.PhtmlTemplate ?>
    <script>
        require([
            'jquery',
            'mage/mage'
        ], function($) {
            var dataFormLogin = $('#login-form');

            dataFormLogin.on('submit', function(e) {
                var isValid = $(this).validation('isValid');
                if (isValid) {
                    $('body').trigger('processStart');
                }
            });
        });
    </script>
    <script type="text/x-magento-init">
        {
            "*": {
                "Magento_Customer/js/block-submit-on-send": {
                    "formId": "login-form"
                },
                "Magento_Ui/js/core/app": {
                    "components": {
                        "showPassword": {
                            "component": "Magento_Customer/js/show-password",
                            "passwordSelector": "#pass"
                        }
                    }
                }
            }
        }
    </script>
</div>
