<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Home</item>
                    <item name="label" xsi:type="string">Home</item>
                    <item name="link" xsi:type="string">/</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Login</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Login</item>
                    <item name="label" xsi:type="string">Login</item>
                </argument>
            </action>
        </referenceBlock>

        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Rewards Member Login</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="customer.login.container">
            <referenceBlock name="customer.new" remove="true"/> 
            <container htmlTag="div" htmlClass="benefits">
                <container name="customer.login.benefits.wrapper" htmlTag="div" htmlClass="note customer-login-benefits-wrapper" before="-">
                    <block class="Magento\Cms\Block\Block" name="customer_login_benefits">
                        <arguments>
                            <argument name="block_id" xsi:type="string">customer_login_benefits</argument>
                        </arguments>
                    </block>
                </container>
            </container>
        </referenceContainer>
         <referenceContainer name="content">
            <block class="Totaltools\OtpLogin\Block\Login\Scripts" name="otp.login.scripts" template="Totaltools_OtpLogin::scripts.phtml" after="customer_form_login"/>
        </referenceContainer>
        <referenceBlock name="company.new.wrapper" remove="true"/>       
    </body>
</page>
