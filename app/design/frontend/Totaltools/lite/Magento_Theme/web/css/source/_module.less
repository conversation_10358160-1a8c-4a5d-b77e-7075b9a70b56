// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

// @import 'module/_collapsible_navigation.less';

//
//  Theme variables
//  _____________________________________________

//  Messages
@message-global-note__background: @color-yellow-light2;
@message-global-note__border-color: @color-yellow-light3;
@message-global-note__color: @text__color;

@message-global-note-link__color: @link__color;
@message-global-note-link__color-hover: @link__hover__color;
@message-global-note-link__color-active: @link__active__color;

@message-global-caution__background: @color-red9;
@message-global-caution__border-color: none;
@message-global-caution__color: @color-white;

@message-global-caution-link__color: @link__color;
@message-global-caution-link__color-hover: @link__hover__color;
@message-global-caution-link__color-active: @link__active__color;

//  Header
@header__background-color: false;
@header-icons-color: @color-gray56;
@header-icons-color-hover: @color-gray20;

@addto-color: @text__color__muted;
@addto-hover-color: @primary__color;

@minicart-icons-color: @header-icons-color;
@minicart-icons-color-hover: @header-icons-color-hover;

@button__shadow: inset 0 2px 1px rgba(0, 0, 0, .12);

@h1__margin-bottom__desktop: @indent__xl;

//  Footer
@footer__background-color: false;

//
//  Common
//  _____________________________________________
& when (@media-common = true) {
    * {
        .lib-css(box-sizing, border-box, 1);
    }

    body {
        color: @color-gray20;
        font-family: @font-family__base;
        font-style: normal;
        .lib-css(font-weight, @font-weight__regular);
        .lib-font-size(16);
        line-height: 1.25;
    }

    .no-display {
        display: none;
    }

    input[type=text],
    input[type=password],
    input[type=url],
    input[type=tel],
    input[type=search],
    input[type=number],
    input[type*=date],
    input[type=email] {
        background: @color-white;
        background-clip: padding-box;
        border: 1px solid @color-gray80;
        .lib-css(border-radius, 3px, 1);
        .lib-font-size(16);
        height: 47px;
        line-height: 45px;
        padding: 0 15px;
        vertical-align: baseline;
        width: 100%;
    }

    textarea {
        background: @color-white;
        border: 1px solid @color-gray80;
        .lib-css(border-radius, 3px, 1);
        .lib-font-size(16);
        font-weight: 400;
        height: auto;
        line-height: 45px;
        margin: 0;
        padding: 10px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box;
        resize: vertical;
    }

    input:not([disabled]):focus,
    textarea:not([disabled]):focus,
    select:not([disabled]):focus {
        .lib-css(box-shadow, 0 0 3px 2px @color-light-blue4, 1);
    }

    input,
    select,
    textarea {
        .mage-error {
            border-color: @color-blue3;
            color: @primary__color;
        }
    }

    div {
        &.field-error,
        &.mage-error {
            color: @color-red6;
            .lib-font-size(12);
        }
    }

    a {
        color: @secondary__color;
        text-decoration: none;

        &:visited {
            color: @secondary__color;
            text-decoration: none;
        }

        &:hover {
            color: @secondary__color;
            text-decoration: underline;
        }
    }

    select {
        background: @color-white;
        background-clip: padding-box;
        border: 1px solid @color-gray80;
        .lib-css(border-radius, 3px, 1);
        .lib-font-size(16);
        .lib-line-height(45);
        .lib-css(font-weight, @font-weight__regular);
        height: 47px;
        padding: 0 40px 0 15px;
        vertical-align: baseline;
        width: 100%;
        .lib-css(appearance, none, 1);
        background: url(../images/arrow-down.png) no-repeat right center;
        color: @color-gray20;
    }

    .field.choice {
        position: relative;
    }

    input[type="checkbox"] {
        .lib-custom-input(
            @_custom-input__size: 22px,
            @_custom-input__border-radius: 3px,
            @_custom-input-check-postion__top: 2px,
            @_custom-input-check-postion__left: 4px,
            @_custom-input-check__content: @tt-icon-check-bold,
            @_custom-input-check__color: @color-blue,
            @_custom-input-check__size: 10px
        );
    }

    input[type="radio"] {
        .lib-custom-input(
            @_custom-input__size: 22px,
            @_custom-input__border-radius: 50%,
            @_custom-input-check-postion__top: 7px,
            @_custom-input-check-postion__left: 7px,
            @_custom-input-check__size: 10px,
            @_custom-input-check__background-color: @color-blue,
            @_custom-input-check__border-radius: 50%,
        );
    }

    table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        max-width: 100%;

        > caption {
            border: 0px;
            clip: rect(0px, 0px, 0px, 0px);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0px;
            position: absolute;
            width: 1px;
        }
    }

    .link-now {
        background-color: @secondary__color;
        border-radius: 2px;
        border: 2px solid darken(@secondary__color, 5%);
        display: inline-block;
        .lib-css(transition, all 0.3s ease-in-out);
        vertical-align: middle;

        span {
            span {
                color: @color-white;
                display: inline-block;
                font-family: @font-family-name__base;
                .lib-css(font-weight, @font-weight__bold);
                font-size: 14px;
                height: 34px;
                line-height: 34px;
                padding: 0 18px;
                .lib-css(transition, all 0.3s ease-in-out);
                vertical-align: middle;
            }
        }

        &:hover {
            background-color: lighten(@secondary__color, 10%);
            border-color: lighten(@secondary__color, 5%);
            .lib-css(transition, all 0.3s ease-in-out);

            span {
                span {
                    color: @color-white;
                    .lib-css(transition, all 0.3s ease-in-out);
                }
            }
        }
    }

    .cms-page-view {
        &.page-layout-2columns-left {
            .breadcrumbs {
                display: none;
            }

            .sidebar-main {
                margin-top: 0;

                .menu-cms {
                    border-bottom: 1px solid @color-gray80;
                    border-top: 1px solid @color-gray80;
                    padding-bottom: 13px;
                    padding-top: 10px;
                }
            }
        }
    }

    @media screen and (max-width: 768px) {
        .cms-page-view {
            &.page-layout-2columns-left {
                .sidebar-main {
                    width: 100%;
                }
            }
        }
    }

    h1.title-cms {
        color: @color-gray20;
        font-family: @font-family-name__base;
        .lib-css(font-weight, @font-weight__bold);
        font-size: 28px;
        letter-spacing: -1.1px;
        line-height: 39px;
        margin-bottom: 7px;
        margin-left: -3px;
    }

    .cms-page-view h2 {
        color: @color-gray20;
        font-family: @font-family-name__base;
        .lib-css(font-weight, @font-weight__bold);
        font-size: 36px;
        line-height: 1.2em;
        text-transform: uppercase;
        letter-spacing: -0.65px;
    }

    .cms-page-view h3 {
        color: @color-gray20;
        font-family: @font-family-name__base;
        .lib-css(font-weight, @font-weight__bold);
        font-size: 24px;
        line-height: 32px;
        text-transform: uppercase;
        letter-spacing: -0.65px;
    }

    .wrap-cms {
        .columns .column.main {
            max-width: 100%;
        }

        .banner {
            margin-bottom: 14px;
            border: 1px solid @color-gray-light5;
            padding: 1px;
            background-color: @color-white;

            .banner-fran {
                font-size: 1px;

                .title-banner h2 {
                    color: @color-white;
                    font-size: 50px;
                    display: inline-block;
                    line-height: 91%;
                }
            }
        }

        .content .content-main {
            width: 68.6%;
            float: left;
            padding-top: 15px;

            iframe {
                width: 100%;
                margin-top: 7px;
            }

            .franchise_content {
                p {
                    font-size: 14px;
                    letter-spacing: -0.3px;
                    line-height: 20px;

                    strong {
                        font-size: 15px;
                        letter-spacing: 0.3px;
                        line-height: 1;
                    }
                }

                h3 {
                    margin-top: 35px;
                    letter-spacing: -1px;
                    line-height: 29px;

                    &.best-choice {
                        margin-bottom: 8px;
                        margin-top: 28px;
                    }

                    &.title-interest {
                        margin-top: 50px;
                        letter-spacing: -1.1px;
                        margin-bottom: 9px;
                    }

                    &.title-video {
                        letter-spacing: -1.1px;
                    }

                    &.title-map {
                        letter-spacing: -1.2px;
                    }
                }

                .image-map {
                    margin-bottom: 44px;
                    padding-top: 0;
                }
            }
        }
    }

    .fieldset {
        border: 0;
        margin: 0;
        padding: 0;
    }

    .fieldset>.field.required>.label:after,
    .fieldset>.fields>.field.required>.label:after,
    .fieldset>.field._required>.label:after,
    .fieldset>.fields>.field._required>.label:after {
        content: '*';
        color: @color-red6;
        margin-left: 2px;
    }

    .fieldset {
        &>.field {
            margin: 0 0 20px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        &>.fields {
            &>.field {
                margin: 0 0 20px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .fieldset>.field .note,
    .fieldset>.fields>.field .note {
        .lib-font-size(14);
        margin: 3px 0 0;
        padding: 0;
        display: inline-block;
        text-decoration: none;
    }

    div.field-error,
    div.mage-error {
        margin-top: 5px;
    }

    .loqate-address {
        .note {
            display: block;
            font-size: 12px !important;
            line-height: 1.25;
            margin-bottom: 5px !important;

            span {
                color: @color-red6;
                display: inline;
            }
        }

        &._error {
            input {
                border-color: @color-red6;
            }

            & > span {
                display: block;    
                font-size: 12px;    
                margin-top: 5px;
                color: @color-red;
            }
        }
    }

    .page-main {
        .messages {
            &[role="alert"] {
                margin: 0;

                .message {

                    &.success,
                    &.info,
                    &.error,
                    &.warning,
                    &.notice {
                        margin: 10px 0;
                    }
                }
            }
        }
    }

    .message {
        &.success {
            margin: 0 0 10px;
            padding: 16px 20px;
            display: block;
            .lib-font-size(15);
            .lib-line-height(16);
            background: @color-light-green;
            color: @color-green5;
            padding-left: 56px;
            position: relative;

            &>* {
                &::before {
                    .lib-font-size(20);
                    .lib-line-height(20);
                    color: @color-dark-green1;
                    content: '\e80d';
                    font-family: @font-tt-icons;
                    margin: 0;
                    left: 8px;
                    top: 50%;
                    width: 56px;
                    position: absolute;
                    text-align: center;
                    transform: translateY(-50%);
                }
            }
        }

        &.info {
            margin: 0 0 10px;
            padding: 16px 20px;
            display: block;
            .lib-font-size(15);
            .lib-line-height(16);
            background: @color-light-blue9;
            color: @color-mid-blue2;
            padding-left: 56px;
            position: relative;

            &>* {
                &::before {
                    .lib-font-size(20);
                    .lib-line-height(20);
                    color: @color-mid-blue2;
                    content: '\e821';
                    font-family: @font-tt-icons;
                    margin: 0;
                    left: 8px;
                    top: 50%;
                    width: 56px;
                    position: absolute;
                    text-align: center;
                    transform: translateY(-50%);
                }
            }
        }

        &.error {
            margin: 0 0 10px;
            padding: 16px 20px;
            display: block;
            .lib-font-size(15);
            .lib-line-height(16);
            background: @color-light-red;
            color: @primary__color;
            padding-left: 56px;
            position: relative;

            &>* {
                &::before {
                    .lib-font-size(20);
                    .lib-line-height(20);
                    color: @color-dark-red2;
                    content: '\e80a';
                    font-family: @font-tt-icons;
                    margin: 0;
                    left: 8px;
                    top: 50%;
                    width: 56px;
                    position: absolute;
                    text-align: center;
                    transform: translateY(-50%);
                }
            }
        }

        &.warning {
            margin: 0 0 10px;
            padding: 16px 20px;
            display: block;
            .lib-font-size(15);
            .lib-line-height(16);
            background: @color-light-golden;
            color: @color-golden;
            padding-left: 56px;
            position: relative;

            &>* {
                &::before {
                    .lib-font-size(20);
                    .lib-line-height(20);
                    color: @color-golden;
                    content: '\e822';
                    font-family: @font-tt-icons;
                    margin: 0;
                    left: 8px;
                    top: 50%;
                    width: 56px;
                    position: absolute;
                    text-align: center;
                    transform: translateY(-50%);
                }
            }
        }

        &.notice {
            margin: 0 0 10px;
            padding: 16px 20px;
            display: block;
            .lib-font-size(15);
            .lib-line-height(16);
            background: @color-light-blue9;
            color: @color-mid-blue2;
            padding-left: 56px;
            position: relative;

            &>* {
                &::before {
                    .lib-font-size(20);
                    .lib-line-height(20);
                    color: @color-mid-blue2;
                    content: '\e821';
                    font-family: @font-tt-icons;
                    margin: 0;
                    left: 8px;
                    top: 50%;
                    width: 56px;
                    position: absolute;
                    text-align: center;
                    transform: translateY(-50%);
                }
            }
        }
    }

    .widget {
        &.block-static-block,
        &.block-cms-link {
            margin-bottom: 0 !important;
        }
    }

    // Footer Style Start
    .page-footer {
        overflow: hidden;

        .footer-middle {
            background: @secondary__color;

            .footer_middle_left {
                p {
                    margin: 0;

                    a {
                        color: @color-white;
                        font-family: @font-family__base;
                        .lib-css(font-weight, @font-weight__bold);
                        letter-spacing: .15px;
                        .lib-font-size(18);
                        .lib-line-height(22);
                        .lib-css(text-transform, uppercase);

                        span {
                            .lib-css(font-weight, @font-weight__regular);
                            .lib-css(text-transform, capitalize);
                        }
                    }
                }
            }

            .footer_middle_right {
                p {
                    color: @color-white;
                    font-family: @font-family__base;
                    .lib-css(font-weight, @font-weight__bold);
                    letter-spacing: .15px;
                    .lib-font-size(18);
                    .lib-line-height(22);
                    margin: 0;
                    .lib-css(text-transform, uppercase);
                }
            }
        }

        .footer-bottom {
            h2 {
                font-family: @font-family__base;
                .lib-font-size(16);
                .lib-line-height(28);
                .lib-css(font-weight, @font-weight__bold);
                .lib-css(text-transform, uppercase);
            }

            .links {
                list-style: none;

                &>li {
                    margin: 0;
                    .lib-line-height(24);

                    a {
                        color: @color-gray20;
                        font-family: @font-family__base;
                        .lib-font-size(14);
                        .lib-line-height(14);

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }

            .socials {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(align-items, center, 1);
                list-style: none;
                margin: 0;

                li {
                    margin: 0;

                    &:last-child {
                        margin-right: 0;
                    }

                    a {
                        .lib-font-size(0);
                        text-align: center;
                        border-radius: 50%;
                        .lib-vendor-prefix-display;
                        .lib-css(align-items, center, 1);
                        .lib-css(justify-content, center, 1);
                        text-decoration: none;

                        &.icon-facebook {
                            background: @color-blue4;

                            &::before {
                                font-size: 24px;
                                line-height: 44px;
                                color: @color-white;
                                content: '\e804';
                                .lib-css(font-family, @font-tt-icons);
                                vertical-align: middle;
                                display: inline-block;
                                font-weight: 400;
                                text-align: center;
                            }
                        }

                        &.icon-instagram {
                            background: @color-gray3;

                            &::before {
                                font-size: 24px;
                                line-height: 44px;
                                color: @color-white;
                                content: '\e805';
                                .lib-css(font-family, @font-tt-icons);
                                vertical-align: middle;
                                display: inline-block;
                                font-weight: 400;
                                text-align: center;
                            }
                        }

                        &.icon-youtube {
                            background: @color-orange;

                            &::before {
                                font-size: 24px;
                                line-height: 44px;
                                color: @color-white;
                                content: '\e807';
                                .lib-css(font-family, @font-tt-icons);
                                vertical-align: middle;
                                display: inline-block;
                                font-weight: 400;
                                text-align: center;
                            }
                        }
                    }
                }
            }
        }

        .footer-copyright {
            background: @primary__color;

            .copyright {
                .lib-font-size(14);
                .lib-line-height(17.5);
                color: @color-white;
                font-family: @font-family__base;
            }

            .checkout-index-index & {
                position: relative;
                .lib-clearfix;

                .footer.content {
                    &:before {
                        content: '';
                        width: 142px;
                        height: 109px;
                        position: absolute;
                        left: 20px;
                        top: -29px;
                        z-index: 1;
                        background: url(../images/footer-logo.png) no-repeat;
                    }

                    .copyright {
                        width: 50%;
                        text-align: left;
                        float: right;
                        padding: 0;
                        margin: @indent__s @indent__m @indent__base;
                    }
                }
            }

            .payments-wrapper {
                &>div {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                }

                p {
                    .lib-font-size(14);
                    .lib-line-height(34);
                    color: @color-white;
                    font-family: @font-family__base;
                }

                .payments {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                    margin: 0;
                    padding: 0;
                    list-style: none;

                    li {
                        margin: 0 0 0 6px;
                        width: 50px;
                        height: 32px;

                        &.rapid {
                            width: 111px;
                            height: 54px;

                            img {
                                margin-top: -7px;
                            }
                        }

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }

                        a {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
        }
    }

    .action-floating {
        position: fixed;
        z-index: 1000;
        bottom: 16px;
        right: 16px;
    }

    .action-backtotop {
        display: inline-block;
        width: 50px;
        height: 50px;
        text-align: center;
        visibility: hidden;
        .lib-css(opacity, 0, 1);
        .lib-css(border-radius, 4px, 1);
        .lib-css(transition, ~"background-color .3s, opacity .5s, visibility .5s", 1);
        border: 1px solid @color-gray-darken2;

        &:hover {
            border: 1px solid @color-gray-darken2;
        }

        .lib-icon-font(
            @tt-icon-arrow-up,
            @_icon-font-size: 18px,
            @_icon-font-color: @color-white,
            @_icon-font-text-hide: true
        );

        &:before {
            .lib-css(transition, transform 0.3s ease-in-out, 1);
        }

        &.show {
            .lib-css(opacity, 1, 1);
            visibility: visible;

            &:hover {
                &:before {
                    .lib-css(transform, translateY(-3px), 1);
                }
            }
        }
    }

    @media screen and (max-width: 400px) {
        .page-footer {
            .footer-bottom {
                .block-content {
                    padding-bottom: 12px;
                }

                .footer.links {
                    li {
                        .lib-line-height(1);
                    }
                    a {
                        .lib-font-size(14);
                        .lib-line-height(26);
                        .lib-css(letter-spacing, 1px, 1);
                    }
                }
            }

            .footer-copyright {
                .footer {
                    &.content {
                        .copyright {
                            margin: 0;
                        }
                    }
                }
            }
        }
    }


    // Footer Style End

    .cms-home {
        .page-wrapper {
            background: @primary__color;
        }

        .page-top-banner,
        .page-footer {
            background: @color-white;
        }

        .columns {
            .column {
                &.main {
                    padding-bottom: 15px;
                }
            }
        }
    }

    .home-top-banner {
        .homepage-boxes {
            .homepage-boxes-list {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(nowrap);

                .slick-slide {
                    .lib-font-size(0);
                    .lib-line-height(0);
                }

                .homepage-box {
                    .lib-css(flex, 0 0 100%, 1);

                    .homepage-box-link {
                        display: block;
                    }

                    .homepage-box-image {
                        display: block;

                        img {
                            width: 100%;
                        }
                    }
                }

                .slick-arrow {
                    background: @color-black-75;
                    z-index: 3;

                    &::before {
                        .lib-font-size(24);
                        color: @color-white;
                    }

                    &:hover {
                        background: @color-black;
                    }

                    &.slick-prev {
                        &::before {
                            padding-right: 2px;
                        }
                    }

                    &.slick-next {
                        &::before {
                            padding-left: 2px;
                        }
                    }
                }

                .slick-dots {
                    display: none !important;
                }
            }
        }
    }

    .homepage-top-brands {
        .homepage-brands-container {
            .lib-vendor-prefix-display;
            margin: 0;
            width: 100%;
        }

        .slick-arrow {
            top: 50%;
            width: 28px;
            height: 42px;
            background: @color-black-75;

            &::before {
                .lib-font-size(24);
                color: @color-white;
            }

            &:hover {
                background: @color-black;
            }

            &.slick-prev {
                left: 0;

                &::before {
                    padding-right: 2px;
                }
            }

            &.slick-next {
                right: 0;

                &::before {
                    padding-left: 2px;
                }
            }
        }
    }

    .block-banners {
        ul {
            margin: 0;
            padding: 0;

            li {
                margin: 0;
            }
        }
    }

    .homepage-block {
        background: @color-white;
        .lib-css(border-radius, 4px, 1);
        margin-bottom: 15px;
        padding: 20px 15px 0;

        .product-notforsale-container & {
            padding-left: 0;
            padding-right: 0;
        }

        h2 {
            color: @color-gray20;
            .lib-font-size(24);
            .lib-line-height(28);
            font-family: @font-family__base;
            .lib-css(font-weight, @font-weight__bold);
            margin: 0 0 20px;
        }

        .products-grid {
            margin: 0;
            padding: 0;
            .product-items {
                margin: 0;
                padding: 0;
                list-style: none;
                display: block;
            }

            .slick-arrow {
                top: 50%;
                width: 28px;
                height: 42px;
                background: @color-black-75;
                z-index: 1;

                &::before {
                    .lib-font-size(24);
                    color: @color-white;
                }

                &:hover {
                    background: @color-black;
                }

                &.slick-prev {
                    left: 0;

                    &::before {
                        padding-right: 2px;
                    }
                }

                &.slick-next {
                    right: 0;

                    &::before {
                        padding-left: 2px;
                    }
                }
            }
        }

        .pagebuilder-column-group {
            .pagebuilder-column-line {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
                width: 100%;
            }
        }
    }

    .product-recently-view {
        .emarsys-recommended {
            &.homepage-block {
                padding-left: 0;
                padding-right: 0;
            }
        }
    }

    figure {
        &.homepage-block {
            margin: 0;
            padding-bottom: 15px;
        }
    }

    .subcategories-wrapper {
        background: @color-white;
        position: relative;
        padding: 15px;
        margin-bottom: 0;
        .lib-css(border-radius, 3px, 1);

        h2 {
            .lib-css(font-weight, @font-weight__bold);
            text-align: center;
            .lib-css(text-transform, uppercase);
            letter-spacing: -1px;
            word-spacing: -1px;
        }

        .subcategories-list {
            width: 100%;
            padding: 0;
            margin: 0;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);

            .item {
                position: relative;
                display: block;
                margin: 0;
                text-align: center;

                a {
                    display: block;

                    img {
                        max-width: 100%;
                        max-height: 90px;
                    }
                }
            }
        }

        .actions {
            text-align: center;
            padding: 15px 0;

            .action-view {
                .lib-font-size(17);
                .lib-line-height(40);
                .lib-css(border-radius, 3px, 1);
                margin: 0;
                padding: 2px 18px;
                letter-spacing: -.25px;
                .lib-css(font-weight, @font-weight__bold);
                background: @color-white;
                border: 2px solid @color-blue5;
                color: @secondary__color;
                font-family: @font-family__base;
                text-decoration: none;
                display: inline-block;
                .lib-css(text-transform, uppercase, 1);

                &:hover {
                    background-color: @secondary__color;
                    color: @color-white;
                    text-decoration: none;
                }
            }
        }
    }

    // datepicker
    .ui-datepicker.ui-widget {
        width: 300px;
        box-sizing: border-box;
        margin-top: 16px;
        border: none;
        padding: 0;
        box-shadow: 5px 5px 10px 0px @color-black-10;
        border-radius: 0 0 5px 5px;
        overflow: hidden;
        background: @color-gray14;
        z-index: 5 !important;
        display: none;

        .loyalty-insider-invoices & {
            margin-left: -42px;
        }

        .ui-datepicker-prev {
            left: 11px;
            top: 3px;
            position: absolute;
            width: 28px;
            height: 28px;
            cursor: pointer;
        }

        .ui-datepicker-next {
            right: 11px;
            top: 3px;
            position: absolute;
            width: 28px;
            height: 28px;
            cursor: pointer;
        }

        .ui-datepicker-header {
            background: @secondary__color;
            padding: 5px 0;
            position: relative;
            border: none;

            .ui-datepicker-title {
                color: @color-white;
                font-weight: @font-weight__bold;
                .lib-font-size(14px);
                border: none;
                margin: 0 30px;
                .lib-line-height(28);
                text-align: center;

                .ui-datepicker-month {
                    width: 47%;
                    margin-right: 6%;
                }

                .ui-datepicker-year {
                    width: 47%;
                }
            }
        }

        .ui-datepicker-prev {
            span {
                display: block;
                position: absolute;
                left: 50%;
                margin-left: -8px;
                top: 50%;
                margin-top: -5px;
                font-size: 0px;
                width: 0;
                height: 0;
                border: solid;
                border-width: 5px 8px 5px 0;
                border-color: transparent @color-white transparent transparent;
            }
        }

        .ui-datepicker-next {
            span {
                display: block;
                position: absolute;
                left: 50%;
                margin-left: 0;
                top: 50%;
                margin-top: -5px;
                font-size: 0px;
                width: 0;
                height: 0;
                border: solid;
                border-width: 5px 0 5px 8px;
                border-color: transparent transparent transparent @color-white;
            }
        }

        .ui-datepicker-calendar {
            border: none;

            .ui-state-disabled {
                background: @color-gray99;
            }

            .ui-state-active {
                background: @secondary__color;
                color: @color-white;
            }

            th {
                background: @color-white;
                border-width: 0 0 1px 0;
                border-color: @color-gray93;
                padding: 12px 10px;
                text-transform: capitalize;
                text-align: center;
                .lib-font-size(10px);
                font-weight: @font-weight__regular;
            }

            td {
                border: none;
                padding: 0;
                text-align: center;
                .lib-font-size(13);
                background: @color-white;

                &.ui-datepicker-today {
                    background: @secondary__color;

                    span,
                    a {
                        color: @color-white;
                    }
                }

                .ui-state-hover {
                    background: @secondary__color;
                    color: @color-white;
                }

                span,
                a {
                    display: block;
                    padding: 10px 8px;
                    text-align: center;
                    text-decoration: none;
                    color: @color-gray20;
                }
            }
        }

        select {
            height: 26px;
            line-height: 26px;
            margin: 0;
            border-color: @color-white;
            color: @color-white;

            option {
                color: @text__color;
            }
        }

    }

    #add-cart-notifications {
        max-width: 300px;
        position: fixed;
        z-index: 999999;

        &.top-right {
            top: 15%;
            right: @indent__m;
        }

        &.top-left {
            top: 15%;
            left: @indent__m;
        }

        &.bottom-right {
            bottom: 10%;
            right: 15%;
        }

        &.bottom-left {
            bottom: 10%;
            left: 15%;
        }

        .notification {
            background-color: rgba(247, 247, 247, 0.98);
            position: relative;
            padding: 15px;
            .lib-css(margin-bottom, @indent__base);
            .lib-css(border-radius, 3px, 1);
            .lib-css(box-shadow, 0 0 5px @color-black-25, 1);
            .lib-css(transition, all 0.2s ease, 1);

            .close {
                display: block;
                position: absolute;
                text-align: center;
                color: @color-gray-light6;
                width: 20px;
                height: 20px;
                top: 15px;
                right: 10px;
                cursor: pointer;
                .lib-font-size(14);
                .lib-css(transition, color 0.2s ease, 1);

                .lib-icon-font(
                    @_icon-font-content: '\e906',
                    @_icon-font-text-hide: true
                    );

                &:hover {
                    color: @color-gray40;
                }
            }

            .notification-title {
                text-transform: initial;
                letter-spacing: normal;
                text-align: center;
                margin: 0 0 15px;
                .lib-line-height(18);
                .lib-css(font-family, @font-family__base);
                .lib-css(color, @primary__color);
            }

            .notification-message {
                width: 100%;
                text-align: center;
                display: none;
                .lib-css(font-weight, @font-weight__bold);

                body.test_36 & {
                    display: block;
                    .lib-css(margin-top, @indent__s);
                }
            }

            &.cart-reminder {
                .notification-message {
                    display: block;
                }
            }

            .notification-content {
                .lib-vendor-prefix-display();
                .lib-vendor-prefix-flex-wrap();

                .product-thumb {
                    width: 70px;
                    height: 70px;
                    border: 2px solid @color-gray90;
                    background: @color-white;
                    padding: 3px;
                    text-align: center;
                    .lib-css(box-sizing, border-box, 1);

                    img {
                        max-width: 100%;
                    }
                }

                .product-name {
                    width: ~"calc(100% - 70px)";
                    margin-left: auto;
                    padding-left: 15px;
                    .lib-css(box-sizing, border-box, 1);
                }

                .cart-link {
                    width: 100%;
                    margin-top: 15px;

                    a {
                        display: block;
                        width: 100%;
                        text-align: center;
                        .lib-css(font-family, @font-family__base);
                        .lib-css(border, 2px solid @color-red5);
                        .lib-css(background-color, @primary__color);
                        .lib-css(border-radius, 3px, 1);
                        color: @color-white;
                        .lib-css(font-weight, @font-weight__bold);
                        .lib-css(text-decoration, unset);
                        padding: 7px 15px;
                        .lib-font-size(16);
                        .lib-line-height(24);
                    }
                }

                &.locate-store {
                    .store-name {
                        padding-right: 20px;
                        margin-bottom: 12px;

                        span {
                            display: block;
                        }
                    }

                    .store-link { 
                        width: 100%;
                        margin-bottom: 10px;
                        
                        .action {
                            text-align: center;
                            width: 100%;
                            background-color: @color-red2;
                            border-color: @color-red;

                            &:hover {
                                background-color: @color-red3;
                            }
                        }
                    }

                    .change-store-btn {
                        width: 100%;
                    }
                }
            }
        }
    }

    .ui-hidden,
    .hidden {
        display: none;
        visibility: hidden;
        .lib-css(opacity, 0, 1);
    }

    // 404 page CSS
    .cms-no-route {
        .columns {
            .column.main {
                h1 {
                    margin-bottom: 40px;
                    .lib-css(font-weight, @font-weight__bold);
                }

                p {
                    margin-bottom: 20px;
                }
                strong {
                    display: block;
                    margin-bottom: 25px;
                }
                ul {
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    li {
                        margin-bottom: 10px;
                    }
                }
            }
        }
        .page-title-wrapper {
            .page-title {
                .lib-css(font-weight, @font-weight__bold);
            }
        }
    }

    // loader page CSS
    .admin__data-grid-loading-mask {
        z-index: 50;

        .spinner {
            height: 64px;
            width: 64px;
            margin: 0;
            transform: translate(-50%, -50%);
        }
    }

    .cms-insider-rewards {
        .register-block {
            display: none;
        }


        .signup-block {
            display: none;
        }

        .register-block {
            display: block;
        }


        .insider-ctas {
            margin: @indent__base auto;
            text-align: center;
        }
    }

    @media screen and (min-width: @screen__xxs) and (max-width: @screen__m) {
        .cms-insider-rewards {
            .how-it-works {
                img {
                    max-width: 45%;
                }
            }
        }
    }

    // Search overrides
    .search-autocomplete {
        &-product {
            .info {
                .mpn {
                    .lib-font-size(14);
                    .lib-css(font-family, @font-family__base);
                    .lib-css(font-weight, @font-weight__regular);
                    display: none;
                }

                .price-box .product-price .currency-symbol {
                    top: 3px;
                    .lib-heading-typography(16, 16);
                }

                .price-box .product-price .price-main {
                    .lib-heading-typography(30, 30);

                    .price-decimal {
                        top: 3px;
                        .lib-heading-typography(16, 16);
                    }
                }
            }

            a:hover {
                text-decoration: none;
            }
        }
    }

    // CMS Careers page
    .cms-careers {
        #turborecruitFrame{
            width: 100%;
        }
    }

    // CMS Franchise Opportunities page
    .triangle {
        background-color: @secondary__color;
        display: inline-block;
        width: 100%;

        .content-triangle {
            display: inline-block;
            float: left;
            width: 48%;

            .after-pd {
                padding: 51px 0px 20px 38px;
            }
        }

        .image-banner {
            display: inline-block;
            float: right;
            margin-left: -10%;
            width: 56.5%;
        }

        .des-triangle {
            margin-top: 20px;
        }

        p {
            color: @color-white;
            font-weight: 700;
            font-size: 18px;
            letter-spacing: 0.1px;
            line-height: 22px;
        }

        .link-now {
            border: 2px solid @color-white;
        }

        .image-banner {
            img {
                float: right;
            }
        }
    }

    .wrap-cms {
        .banner {
            .banner-fran {
                .title-banner {
                    h2 {
                        color: @color-white;
                        font-size: 50px;
                        display: inline-block;
                        line-height: 91%;
                        background-color: @primary__color;
                        height: auto;
                        margin: 0;
                        max-width: 372px;
                        text-transform: uppercase;
                        letter-spacing: -1.8px;

                        &:first-child {
                            padding: 13px 18px 3px 11px;
                        }

                        &:last-child {
                            padding: 4px 19px 3px 11px;
                        }
                    }
                }
            }
        }

        .content {
            .content-main {
                width: 68.6%;
                float: left;
                padding-top: 15px;

                iframe {
                    width: 100%;
                    margin-top: 7px;
                }
            }

            .content-right {
                width: 31.4%;
                float: left;

                .franchise-sidebar {
                    padding: 0 0 0 15px;

                    &>.content {
                        border-radius: 3px;
                        border: 2px solid @color-gray90;
                        padding: 20px;
                        background-color: @color-gray97;
                        margin-top: 6px;
                        padding-top: 23px;
                    }
                }
            }
        }
    }

    .testimonials {
        padding-bottom: 0;
        border-bottom: 1px solid @color-gray90;

        ul {
            list-style-type: none;
            padding: 0;
            margin-bottom: 0;

            li {
                margin-top: 0;
                margin-bottom: 1rem;

                &:last-child {
                    margin-bottom: 5px;
                }
            }
        }

        .author-say {
            font-style: italic;
            color: @color-gray20;
            font-size: 18px;
            line-height: 24px;
            letter-spacing: -.6px;
        }

        .author {
            display: inline-block;
            width: 100%;
            margin-top: 17px;
            margin-bottom: 8px;

            .image {
                display: inline-block;
                float: left;
                margin-right: 0;
                width: 30%;
            }

            .name-job {
                float: right;
                width: 70%;

                .author-name {
                    margin-top: 9px;
                    margin-bottom: 2px;
                    padding-left: 5px;
                    color: @color-gray20;
                    font-size: 16px;
                    line-height: 16px;
                }

                .author-job {
                    padding-left: 6px;
                    font-size: 14px;
                    line-height: 20px;
                }
            }
        }
    }

    .investment {
        padding-top: 16px;
        padding-bottom: 18px;

        h4 {
            font-weight: 700;
            font-size: 16px;
            color: @color-gray20;
            line-height: 20px;
            text-transform: uppercase;
            margin-top: 26px;
        }

        .box-download {
            position: relative;
        }
    }

    @media (max-width: 1199px) {
        .triangle {
            .content-triangle {
                width: 100%;

                .title-banner {
                    display: flex;
                    flex-direction: column;

                    h2 {
                        width: 100%;
                    }
                }
            }

            .image-banner {
                display: none;
            }

            .des-triangle {
                padding-right: 20px;
            }
        }
    }

    @media (max-width: 767px) {
        .triangle {
            .content-triangle {
                .after-pd {
                    padding: 40px 0;
                    text-align: left;
                }

                .title-banner {
                    margin: auto auto 20px;
                    text-align: left;
                    padding-left: 20px;

                    h2 {
                        .lib-font-size(45);
                    }
                }
            }
            .des-triangle {
                padding: 0 20px;
            }

            .link-now {
                margin-left: 20px;
            }
        }

        .wrap-cms {
            .content {
                .content-main {
                    width: 100%;
                }

                .content-right {
                    width: 100%;

                    .franchise-sidebar {
                        padding: 0;
                    }
                }
            }
        }

        .testimonials {
            .author {
                max-width: 300px;
            }
        }
    }

    // CMS business reward page
    .trade-rewards {
        position: relative;

        img {
            position: relative;
            z-index: 1;

            &.trade-rewards-desktop {
                width: 100%;
            }

            &.trade-rewards-mobile {
                width: 100%;
            }
        }

        a {
            border-radius: 8px;
            .lib-css(text-decoration, none);
            display: inline-block;
            text-transform: capitalize;
            position: absolute;
            z-index: 2;
        }
    }

    .rewards-card-wrap {
        h2 {
            text-align: center;
        }

        .rewards-row {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(justify-content, center, 1);
            margin: 0 -12.5px 16px;
            row-gap: 19px;

            .rewards-col {
                padding: 0 12.5px;
                text-align: center;
                .lib-css(box-sizing, border-box, 1);

                &:nth-child(2n) {
                    .trade-reward {
                        background: @color-gray14;
                    }
                }

                .trade-reward {
                    margin: 0 auto;
                    padding: 13px 9px 20px;
                    background: @color-gray-light9;
                    height: 100%;
                    .trade-reward-row {
                        .trade-reward-img {
                            img, svg {
                                max-width: 191px;
                                height: 88px;
                                margin: 0 auto 13px;
                                object-fit: contain;
                            }
                        }

                        .trade-reward-content {
                            h4 {
                                .lib-heading-typography(20, 22, @secondary__color);
                                margin: 0 auto 13px;
                                min-height: 44px;
                                max-width: 230px;
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap;
                                .lib-css(align-items, center, 1);
                                .lib-css(justify-content, center, 1);
                            }
                        }
                    }

                    span {
                        .lib-font-size(16);
                        .lib-line-height(20);
                        color: @secondary__color;
                        .lib-css(font-weight, @font-weight__bold);
                        margin-bottom: 15px;
                        display: block;
                        text-align: left;
                    }

                    p {
                        .lib-font-size(16);
                        .lib-line-height(19);
                        color: @secondary__color;
                        margin-bottom: 11px;
                        text-align: left;
                    }

                    ul {
                        text-align: left;
                        margin: 0 auto;
                        padding: 0;
                        list-style: none;

                        li {
                            .lib-font-size(16);
                            .lib-line-height(19);
                            margin-bottom: 15px;
                            position: relative;
                            padding-left: 30px;
                            color: @secondary__color;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            .lib-icon-font(@_icon-font-content:  @icon-arrow-up,
                                @_icon-font-display: block,
                                @_icon-font-size: 12px,
                                @_icon-font-position: before);

                            &::before {
                                width: 19px;
                                height: 19px;
                                border-radius: 19px;
                                transform: rotate(90deg);
                                background: @secondary__color;
                                color: @color-white;
                                .lib-vendor-prefix-display;
                                .lib-css(align-items, center, 1);
                                .lib-css(justify-content, center, 1);
                                position: absolute;
                                left: 0;
                                top: 0;
                            }
                        }
                    }

                    .unlock-img {
                        display: block;
                        margin: 15px auto 0;
                        max-width: 140px;
                    }

                    a {
                        .lib-heading-typography(15, 20, @secondary__color);
                        border-radius: 8px;
                        border: 3px solid @secondary__color;
                        display: block;
                        .lib-css(text-decoration, none);
                        padding: 8px 35px 8px 30px;
                        text-align: center;
                        margin: 20px auto 0;
                        position: relative;

                        .lib-icon-font(@_icon-font-content: @icon-arrow-up,
                            @_icon-font-display: block,
                            @_icon-font-size: 20px,
                            @_icon-font-position: after);

                        &::after {
                            color: @secondary__color;
                            transform: rotate(90deg) translateY(-50%);
                            line-height: 1;
                            position: absolute;
                            top: ~"calc(50% - 10px)";
                            right: 20px;
                        }

                        &.coming-soon {
                            pointer-events: none;
                            padding: 8px 7px;

                            &::after {
                                display: none;
                            }
                        }
                    }

                    .more-info {
                        width: 18px;
                        height: 18px;
                        border: 0;
                        font-size: 12px;
                        line-height: 18px;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        position: static;
                        margin: 0;
                        padding: 0;
                        color: @color-white;
                        background: @color-blue;
                        border-radius: 50%;

                        &::after {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .trade-rewards-promo-banner {
        position: relative;
        font-size: 0;
        margin-bottom: 8px;

        .promo-banner-img {
            display: block;
            margin: 0 auto;
            position: relative;

            .promo-banner-desktop {
                max-width: 100%;
            }
            
            .promo-banner-mobile {
                max-width: 100%;
            }
        }
    }

    .register-trade-rewards-wrap {
        background: @secondary__color;
        margin-bottom: -32px;
        position: relative;

        .trade-rewards-img {
            width: 521px;
            max-height: unset;
            object-fit: contain;
            position: relative;
        }

        .register-trade-rewards {
            text-align: center;
            position: relative;

            h4 {
                .lib-heading-typography(24, 34, @color-white);
                margin: 0 0 4px;

                span {
                    color: @color-yellow;
                }
            }

            p {
                font-size: 24px;
                line-height: 28px;
                color: @color-white;
                margin: 0 0 8px;
            }

            a {
                border-radius: 8px;
                border: 4px solid @color-white;
                display: block;
                .lib-css(text-decoration, none);
                margin: 0 auto;
                display: block;
                text-transform: capitalize;
            }
        }
    }

    .googlepay-minicart-logo,
    .braintree-applepay-minicart {
        display:none;
    }

    // CMS Delivery page 
    div[data-content-type="tabs"] {
        .tabs-navigation {
            overflow-x: auto;
            white-space: nowrap;
            border: 0 !important;

            li {
                &.tab-header {
                    &.ui-tabs-tab {
                        border: 0 !important;
                        background:@color-blue;
                        margin: 0 !important;

                        a {
                            color: @color-white;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 14px;
                            line-height: 1;
                            text-align: center;
                            text-decoration: unset !important;

                            &:hover {
                                background-color: @color-black-40;
                            }
                        }

                        &.ui-tabs-active {
                            a {
                                color: @color-white;
                                background: @color-black-15;

                                &:hover {
                                    background-color: @color-black-40;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.custom-tabs {
            padding: 25px 15px 0;

            .tabs-navigation {
                .lib-vendor-prefix-display;

                li {
                    width: 100%;
                    display: block;

                    &.tab-header {
                        &.ui-tabs-tab {
                            a {
                                padding: 16px 5px 14px;
                                font-size: 12px;
                                .lib-css(font-weight, @font-weight__semibold);
                                text-transform: capitalize;
                                height: 100%;
                                min-width: 70px;
                            }
                        }
                    }
                }
            }

            &.with-border {
                .tabs-navigation {
                    li {
                        &.tab-header {
                            &.ui-tabs-tab {
                                border-right: 1px solid @color-white-45 !important;

                                &:last-child {
                                    border-right: 0 !important;
                                }
                            }
                        }
                    }
                }
            }

            .tabs-content {
                border: 0 !important;
                padding-top: 20px !important;
                box-sizing: border-box;
            }
        }

        &.delivery-tabs {
            padding: 25px 15px 0;

            .tabs-navigation {
                .lib-vendor-prefix-display;
                li {
                    width: 100%;
                    display: block;

                    &.tab-header {
                        &.ui-tabs-tab {
                            a {
                                padding: 18px 5px 14px;
                                font-size: 12px;
                                .lib-css(font-weight, @font-weight__semibold);
                                text-transform: capitalize;
                                height: 100%;
                                .lib-vendor-prefix-display;
                                .lib-css(align-items, center, 1);
                                .lib-css(flex-direction, column, 1);
                                .lib-css(justify-content, flex-end, 1);
                                min-width: 70px;

                                span {
                                    max-width: 70px;
                                    margin: 0 auto;
                                }
                            }

                            &.click-collect {
                                a {
                                    &::before {
                                        content: '\e904';
                                        font-size: 25px;
                                        line-height: 1;
                                        .lib-css(font-weight, @font-weight__regular);
                                        .lib-css(color,@color-white);
                                        .lib-css(font-family, @font-tt-icons);
                                        display: inline-block;
                                        margin-bottom: 5px;
                                    }
                                }
                            }

                            &.free-delivery,
                            &.standard-delivery {
                                a {
                                    &::before {
                                        content: '\f0d1';
                                        font-size: 25px;
                                        line-height: 1;
                                        .lib-css(font-weight, @font-weight__regular);
                                        .lib-css(color,@color-white);
                                        .lib-css(font-family, @font-tt-icons);
                                        display: inline-block;
                                        margin-bottom: 5px;
                                    }
                                }
                            }

                            &.express-delivery {
                                a {
                                    &::before {
                                        content: '\f0d1';
                                        font-size: 25px;
                                        line-height: 1;
                                        .lib-css(font-weight, @font-weight__regular);
                                        .lib-css(color,@color-white);
                                        .lib-css(font-family, @font-tt-icons);
                                        display: inline-block;
                                        margin-bottom: 5px;
                                    }

                                    &::after {
                                        content: '\E82D';
                                        font-size: 14px;
                                        line-height: 1;
                                        z-index: 10;
                                        .lib-css(font-weight, @font-weight__regular);
                                        .lib-css(color,@color-white);
                                        .lib-css(font-family, @font-tt-icons);
                                        transform: translateX(-50%);
                                        position: absolute;
                                        top: 16%;
                                        left: ~"calc(50% + 18px)";
                                    }
                                }
                            }

                            &.uber-delivery {
                                a {
                                    &::before {
                                        content: '';
                                        width: 25px;
                                        height: 25px;
                                        background: transparent url('../images/uber-white-icon.png') center center no-repeat;
                                        background-size: contain;
                                        display: inline-block;
                                        margin-bottom: 5px;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            &.with-border {
                .tabs-navigation {
                    li {
                        &.tab-header {
                            &.ui-tabs-tab {
                                border-right: 1px solid @color-white-45 !important;

                                &:last-child {
                                    border-right: 0 !important;
                                }
                            }
                        }
                    }
                }
            }

            .tabs-content {
                border: 0 !important;
                padding-top: 20px !important;
                box-sizing: border-box;
            }
        }
    }


    // CMS careers page
    .cms-careers {
        .page-wrapper {
            overflow: hidden;
        }
    }

    .careers-banner {
        padding-bottom: 43px;
        img {
            width: 100%;
            &.banner-desktop-img {
                margin-bottom: 18px;
            }
            &.banner-mobile-img {
                display: none;
                margin-bottom: 22px;
            }
        }
        h2 {
            text-transform: uppercase;
            .lib-heading-typography (31, 48, @color-black);
            margin: 0 0 9px;
        }

        ul {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            margin: 0 -7px;
            padding: 0;
            list-style: none;
            &:last-child {
                margin-bottom: 0;
            }
            li {
                padding: 0 7px;

                a {
                    text-transform: uppercase;
                    text-align: center;
                    background: @color-red2;
                    .lib-vendor-prefix-display;
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, center, 1);
                    .lib-css(border-radius, 9px, 1);
                    min-height: 100px;
                    &:hover {
                        text-decoration: unset;
                    }
                }
            }
        }

        iframe {
            width: 100%;
            aspect-ratio: 1440/824;
        }
    }

    .careers-categories {
        position: relative;

        &::before {
            content: "";
            background: @color-gray17;
            transform: translateX(-50%);
            top: 0;
            left: 50%;
            width: 100vw;
            height: 100%;
            position: absolute;
        }

        ul {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            padding: 0;
            position: relative;
            list-style: none;

            li {
                text-align: center;
                margin: 0;
                position: relative;

                .category-content {
                    padding: 0 16px;
                    height: 100%;

                    .category-img {
                        .lib-vendor-prefix-display;
                        .lib-css(align-items, flex-end, 1);
                        .lib-css(justify-content, center, 1);
                        img {
                            max-width: 100%;
                            max-height: 100%;
                            object-fit: contain;
                        }
                    }
                    span {
                        font-size: 26px;
                        line-height: 30px;
                        .lib-css(font-weight, @font-weight__bold);
                        text-transform: uppercase;
                        color: @color-black;
                        display: block;
                    }
                }

                &:hover {
                    .category-content-hover {
                        opacity: 1;
                        pointer-events: unset;
                    }
                }

                .category-content-hover {
                    background: @color-red2;
                    display: flex;
                    align-items: center;
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    opacity: 0;
                    pointer-events: none;
                    z-index: 1;
                    transition: 0.3s all ease;

                    p {
                        text-align: center;
                        color: @color-white;
                        font-weight: 700; 
                        margin: 0;
                    }
                }
            }
        }
    }

    .our-history {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        .lib-css(align-items, center, 1);
        .lib-css(justify-content, space-between, 1);
        padding: 58px 0 50px;

        .our-history-content {
            width: 52.7%;
            padding-right: 32px;
            
            h2 {
                text-transform: uppercase;
                .lib-heading-typography (31, 44, @color-black);
                margin: 0 0 20px;
            }

            h4 {
                .lib-font-size(22);
                .lib-line-height(25px);
                .lib-css(font-weight, @font-weight__bold);
                color: @color-black;
                margin: 0 0 22px;
            }

            p {
                .lib-font-size(22);
                .lib-line-height(25px);
                color: @color-black;
                margin: 0 0 28px;
            }

            a {
                text-transform: uppercase;
                .lib-heading-typography (30, 32, @color-white);
                .lib-css(border-radius, 9px, 1);
                background: @color-blue;
                padding: 11px 32px;
                display: inline-block;
                text-align: center;
                &:hover {
                    text-decoration: unset;
                }
            }
        }

        .our-history-img {
            width: 47.3%;

            img {
                margin: -22px 0;
                max-width: 100%;
            }
        }

        &.metcash {
            .lib-css(flex-direction, row-reverse, 1); 
            padding: 46px 0;
            border-top: 2px solid @color-gray17;

            .our-history-content {
                width: 50%;
                padding-right: 0;

                .metcash-img {
                    max-width: 320px;
                    width: 55%;
                    margin-bottom: 26px;
                }
            }

            .our-history-img {
                width: 50%;
                padding-right: 32px;

                img {
                    margin: 0;
                    max-width: 574px;
                    width: 100%;

                    &.metcash-img {
                        max-width: 195px;
                        margin: 0 auto 13px;
                        display: none;
                    }
                }
            }
        }
    }

    .support-communities {
        position: relative;
        padding: 27px 0 50px;

        &::before {
            content: "";
            background: @color-gray17;
            transform: translateX(-50%);
            top: 0;
            left: 50%;
            width: 100vw;
            height: 100%;
            position: absolute;
        }

        h2 {
            text-transform: uppercase;
            .lib-heading-typography (31, 44, @color-black);
            text-align: center;
            margin: 0 0 40px;
            position: relative;
        }

        ul {
            .lib-vendor-prefix-display;
            .lib-css(align-items, center, 1);
            .lib-css(justify-content, center, 1);
            padding: 0;
            margin: 0;
            list-style: none;
            position: relative;

            li {
                text-align: center;
                margin: 0;
                line-height: 1;

                &:last-child {
                    padding-right: 0;
                }
                img {
                    object-fit: contain;
                }
            }
        }
    }

    .our-team-wrap {
        padding: 44px 0 46px;
        h2 {
            text-transform: uppercase;
            .lib-heading-typography (31, 44, @color-black);
            margin: 0 0 17px;
        }
        p {
            .lib-font-size(22);
            .lib-line-height(25px);
            color: @color-black;
            margin: 0 0 25px;
        }
        .our-team-slider {
            .our-team-slide {
                text-align: center;
                .our-team-img {
                    max-width: 100%;
                    height: 220px;
                    margin: 0 auto 14px;
                    .lib-vendor-prefix-display;
                    .lib-css(align-items, flex-end, 1);
                    .lib-css(justify-content, center, 1);
                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
                span {
                    .lib-font-size(27);
                    .lib-line-height(32);
                    color: @color-black;
                    .lib-css(font-weight, @font-weight__bold);
                    text-transform: capitalize;
                }
            }
            .slick-arrow {
                width: 55px;
                height: 100%;
                top: 0;
                transform: unset;
                background: @color-gray-light9;
                &::before {
                    color: @color-gray-darken4;
                    font-size: 24px;
                    padding: 0;
                }
                &.slick-prev {
                    left: 0;
                }
                &.slick-next {
                    right: 0;
                }
            }
        }
    }

    .members-list {
        padding: 0;

        li {
            display: block;
            font-size: 0;

            img {
                width: 100%;
            }
        }
    }

    .join-team-today {
        background: @color-red2;
        .join-team-today-inner {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            img {
                object-fit: contain;
            }
            .join-team-today-content {
                h2 {
                    text-transform: uppercase;
                    margin: 0 0 9px;
                }
                a {
                    text-transform: uppercase;
                    .lib-css(border-radius, 9px, 1);
                    background: @color-blue;
                    display: inline-block;
                    text-align: center;
                    &:hover {
                        text-decoration: unset;
                    }
                }
            }
        }
    }

    @media (max-width: 400px) {
        .page-footer {
            .footer-bottom {
                min-height: 1273px !important;
            }

            .footer-copyright {
                min-height: 164.5px !important;
            }
        }
    }

    @media (max-width: 374px) {
        .our-team-wrap {
            .our-team-slider {
                padding: 0 25px !important;

                .our-team-slide {

                    .our-team-img {
                        height: 120px !important;
                    }

                    span {
                        .lib-font-size(18) !important;
                        .lib-line-height(22) !important;
                    }
                }
                .slick-arrow {
                    width: 25px !important;

                    &::before {
                        font-size: 20px !important;
                    }
                }
            }
        }

        .careers-categories {
            ul {
                li {
                    .category-content-hover {
                        padding-left: 3px !important;
                        padding-right: 3px !important;

                        p {
                            font-size: 16px !important;
                            line-height: 18px !important;
                        }
                    }
                }
            }
        }
    }

    // Blog Section 
    .blog-wrapper {
        display: none;
        background: @color-white;
        border-radius: 4px;
        margin-bottom: 15px;
        padding: 20px 15px 30px;
        padding-bottom: 30px; 
        &.page-home-bottom {
            h2 {
                font-size: 30px;
                letter-spacing: -1.4px;
                margin-bottom: 6px;
                text-align: center;
                text-transform: uppercase;
                font-weight: 700;
                line-height: 42px;
                margin-top: 0;
            }
            p {
                font-weight: 500;
                font-size: 18px;
                letter-spacing: -0.15px;
                margin: 0 0 32px;
                text-align: center;
            }
        }
        .post-list-wrapper {
            .post-list {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                margin: 0 -0.68%;
                .post-holder {
                    margin: 0 .68%;
                    padding: 16px 15px 73.5px;
                    width: 23.64%;
                    border: 2px solid @color-gray10;
                    border-radius: 3px;
                    margin-bottom: 20px;
                    position: relative;

                    .post-ftimg-hld {
                        a {
                            img {
                                display: block;
                                max-width: 100%;
                                margin: 0;    
                                width: 100%;
                            }
                        }
                    }
                    .post-header {
                        position: relative;
                        h3 {
                            font-size: 18px;
                            line-height: 22px;
                            color: @color-gray20;
                            margin: 14px 0 5px;
                            a {
                                font-size: 18px;
                                line-height: 22px;
                                .lib-css(font-weight, @font-weight__bold);
                                color: @color-gray20;
                            }
                        }
                    }
                    .post-footer {
                        position: absolute;
                        left: 16px;
                        bottom: 16px;
                        width: calc(100% - 32px);

                        .post-read-more {
                            margin: 17px 0 23px;
                            font-weight: 600;
                        }
                    }
                }
            }
        }
        
        .post-read-more {
            font-size: 14px;
            line-height: 1.25;
            color: @color-blue;
            display: inline-block;
        }
        .actions {
            text-align: center;
            margin: 20px 0 0px;
            
            .button2 {
                font-size: 17px;
                line-height: 40px;
                border-radius: 3px;
                padding: 2px 18px;
                letter-spacing: -0.25px;
                background: transparent;
                border: 2px solid @color-blue5;
                color: @color-blue;
                cursor: pointer;
                display: inline-block;
                font-weight: 700;
                text-transform: uppercase;
                &:hover {
                    background-color: @color-blue;
                    color: @color-white;
                    text-decoration: none;
                }
            } 
        }
    }

    .checkout-continue-shopping {
        .continue-shopping-btn {
            display: block;
        }
    }

    .continue-shopping-btn {
        font-size: 16px;
        line-height: 18px;
        font-weight: 700;
        border-radius: 3px;
        text-align: center;
        color: @color-blue;
        background-color: @color-white;
        border: 2px solid @color-blue;
        transition: all 0.2s ease;
        margin: 18px 0 0;
        padding: 10px 15px;
        white-space: nowrap;
        font-family: @font-family-name__base;
        display: none;
        width: 100%;

        &:hover {
            background-color: @color-blue;
            color: @color-white;
            text-decoration: none;
        }
    }

    .navigation-open {
        &.nav-before-open {
            &.nav-open {
                #___ratingbadge_0 {
                    z-index: 900 !important;
                }
            }
        }
    }

    .show-mini-cart {
        #___ratingbadge_0 {
            z-index: 18 !important;
        }
    }

    // CMS AfterPay page
    .cms-afterpay-payment-method {
        .page-wrapper {
            overflow-x: hidden;
        }
    }

    .afterpay-banner {
        position: relative;

        img {
            position: relative;
            z-index: 1;

            &.afterpay-banner-desktop {
                width: 100%;
            }

            &.afterpay-banner-mobile {
                width: 100%;
            }
        }
    }

    .afterpay-sec {
        h3 {    
            text-transform: unset;
            margin: 0 0 16px;
        }

        & > p {
            margin-bottom: 32px;

            &:last-child {
                margin-bottom: 0;
            }
        }


        & > ul {
            padding: 0;
            list-style: none;
            margin-bottom: 32px;

            &:last-child {
                margin-bottom: 0;
            }

            li {
                font-weight: 400;
                margin: 0;
            }
        }

        &.bg-gray {
            position: relative;

            &::before {
                content: "";
                background: rgba(242, 242, 242, 0.95);
                left: 50%;
                top: 0;
                width: 100vw;
                height: 100%;
                transform: translateX(-50%);
                position: absolute;
            }

            & > * {
                position: relative;
            }
        }
        
        .intro-row {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(justify-content, center, 1);
            row-gap: 20px;
            margin: 0 -15px;

            .intro-col {
                width: 100%;
                margin: 0 auto;
                padding: 0 15px;

                .intro-card {
                    display: flex;
                    width: 100%;
                    padding: 20px;
                    border-radius: 16px;
                    background: rgba(242, 242, 242, 0.95);
                    flex-direction: column;
                    min-height: 100%;

                    span {
                        font-weight: 700;
                    }

                    h4 {
                        margin: 0;
                    }

                    p {
                        margin: 0;
                    }
                }
            }
        }
        
        .why-use-row {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(justify-content, center, 1);
            row-gap: 20px;
            margin: 0 -15px;

            .why-use-col {
                width: 100%;
                margin: 0 auto;
                padding: 0 15px;

                .why-use-card {
                    display: flex;
                    width: 100%;
                    padding: 20px;
                    border-radius: 16px;
                    background: rgba(242, 242, 242, 0.95);
                    flex-direction: column;
                    min-height: 100%;

                    span {
                        font-weight: 700;
                    }

                    h4 {
                        margin: 0;
                    }

                    p {
                        margin: 0;
                    }
                }
            }
        }
        
        .for-setup {
            border-radius: 16px;
            background: rgba(242, 242, 242, 0.95);
            padding: 20px;

            h4 {
                font-size: 20px;
                font-weight: 700;
                line-height: 1.2;
                margin: 0 0 20px;
            }

            ul {
                margin: 0;
                padding-left: 20px;

                li {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 18px;
                    margin: 0;
                }
            }
        }
        
        .how-works {
            position: relative;
            border-radius: 16px;
            background: @color-white;
            padding: 20px;

            h4 {
                font-size: 20px;
                font-weight: 700;
                line-height: 1.2;
                margin: 0 0 20px;
            }

            ul {
                margin: 0;
                padding-left: 20px;

                li {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 18px;
                    margin: 0;
                }
            }
        }
    }

    // Add styling for Chatbot
    #launcher {
        bottom: 72px !important;
    }

    div {
		&:has(> div > iframe[title="Close message"]){
            bottom: 142px !important;
        }
    }

    @media (min-width: 541px) {
        iframe[title="Messaging window"] {
            inset: auto 16px 142px auto !important;
            max-height: ~"calc(-155px + 100vh)" !important;
        }
        
        .catalog-product-view {
            iframe[title="Messaging window"] {
                inset: auto 16px 224px auto !important;
                max-height: ~"calc(-235px + 100vh)" !important;
            }
        }
    }

    @media (max-width: 1023px) {
        html {
            &.nav-open {
                #launcher,
                div:has(~ #launcher) {
                    z-index: 19 !important;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__m) {
    .page-footer {
        .footer-middle {
            background: @secondary__color;
            min-height: 84px;

            .footer {
                &.content {
                    padding: 20px 22px;
                }
            }

            .footer_middle_left {
                width: 100%;
                text-align: center;
            }

            .footer_middle_right {
                text-align: center;
                width: 100%;
            }
        }

        .footer-bottom {
            min-height: 1145px;

            .footer {
                &.content {
                    padding: 16px 22px;
                }
            }

            .grid12-4 {
                width: 100%;
            }

            h2 {
                border-top: 2px solid @color-gray80;
                padding: 10px 0 7px;
                margin: 0;
                position: relative;

                &[data-collapsible=true] {
                    &:after {
                        font-size: 26px;
                        line-height: inherit;
                        color: inherit;
                        content: '\E800';
                        .lib-css(font-family, @font-tt-icons);
                        vertical-align: middle;
                        display: inline-block;
                        font-weight: 400;
                        text-align: center;
                        margin: 1px 0 0 10px !important;
                        position: absolute;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                }

                &[aria-expanded=true] {
                    &:after {
                        .lib-css(transform, translateY(-50%) rotate(180deg), 1);
                    }
                }
            }

            .links {
                margin: 0;
                padding: 0;
            }

            .socials {
                padding: 18px 0 0;

                li {
                    margin-right: 10px;

                    a {
                        width: 44px;
                        height: 44px;

                        svg {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }
            }
        }

        .footer-copyright {
            min-height: 147.5px;

            .footer {
                &.content {
                    padding: 16px 3px 18px 7px;
                }
            }

            .copyright {
                margin-top: 20px;
                display: block;
                text-align: center;
            }

            .payments-wrapper {
                margin-top: 5px;

                p {
                    margin: 0;
                    width: 100%;
                    text-align: center;
                }

                .payments {
                    max-width: 450px;
                    width: 100%;
                    .lib-css(justify-content, center, 1);
                    margin: 0 auto;

                    li {
                        margin: 0 3px 5px;

                        &.rapid {
                            display: block;
                            margin-top: 10px;
                            width: 100%;
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }

    .action-backtotop {
        .catalog-product-view &,
        .checkout-index-index & {
            right: 15px;
        }

        .catalog-product-view & {
            bottom: 95px;
        }

        .checkout-index-index & {
            bottom: 70px;
        }
    }

    .homepage-top-brands {
        background: @color-white;
        .lib-css(border-radius, 5px, 1);
        margin: 0 15px 15px;
        padding: 10px;

        .homepage-brands-container {
            .slick-arrow {
                display: none !important;
            }

            .slick-track {
                width: 100% !important;
                transform: unset !important;

                .slick-slide {
                    display: none !important;
                    width: 50% !important;
                    .lib-font-size(0);
                    .lib-line-height(1);
                    border-bottom: 4px solid transparent;

                    &:nth-of-type(even) {
                        border-left: 2px solid transparent;
                    }

                    &:nth-of-type(odd) {
                        border-right: 2px solid transparent;
                    }

                    &.slick-active {
                        display: block !important;
                    }
                }
            }
        }

        .view-more {
            a {
                width: 100%;
                .lib-css(text-transform, uppercase);
                .lib-css(font-weight, @font-weight__bold);
                text-align: center;
                padding: 10px 10px 8px;
                .lib-font-size(18);
                .lib-line-height(22.5);
                font-family: @font-family__base;
                color: @color-white;
                background: @primary__color;
                display: inline-block;
                text-decoration: none;
            }
        }
    }

    .subcategories-wrapper {
        h2 {
            .lib-font-size(24);
            margin: 0 0 20px;
        }

        .subcategories-list {
            .item {
                width: 33.33%;
                padding: 5px;
            }
        }

        .actions {
            .action-view {
                width: 100%;
            }
        }
    }

    .homepage-block {
        .pagebuilder-column-group {
            .pagebuilder-column-line {
                .pagebuilder-column {

                    .pagebuilder-mobile-hidden {
                        display: none;
                    }

                    &:first-child {
                        width: 100% !important;
                        padding-right: 0;
                        margin-bottom: 20px;
                    }

                    &:last-child {
                        width: 100% !important;
                    }
                }
            }
        }
    }

    .home-top-banner {
        .homepage-boxes {
            &.homepage-slideshow {
                margin-bottom: 15px;
            }

            .homepage-boxes-list {
                .slick-arrow {
                    width: 28px;
                    height: 42px;
                    top: 50%;

                    &.slick-prev {
                        left: 20px;
                    }

                    &.slick-next {
                        right: 20px;
                    }
                }
            }
        }
    }

    // 404 page CSS
    .cms-no-route {
        .columns {
            .column.main {
                background-image: url(../images/hammer.jpg) !important;
                background-position: center top 380px;
                background-size: 300px auto;
                background-repeat: no-repeat;

                h1 {
                    padding-bottom: 15px;
                    border-bottom: 1px solid @color-gray80;
                }
            }
        }

        div#emarsys-recommended {
            margin-top: 152px;
        }

        .page-title-wrapper {
            .page-title {
                border-bottom: 1px solid @color-gray80;
                padding-bottom: 15px;
            }
        }
    }

    // CMS business reward page
    .trade-rewards {
        margin: 0 -15px;

        img {
            &.trade-rewards-desktop {
                display: none !important;
            }

            &.trade-rewards-mobile {
                display: block;
            }
        }

        a {
            .lib-heading-typography(16, 28, @color-white);
            border: 3px solid @color-white;
            padding: 0 11px;
            left: 5%;
            bottom: 15px;
        }
    }

    .rewards-card-wrap {
        padding: 37px 0 12px;

        h2 {
            .lib-heading-typography(24, 34, @secondary__color);
            margin: 0 0 28px;
        }

        .rewards-row {
            margin: 0;
            row-gap: 28px;
            .lib-css(flex-direction, column, 1);

            .rewards-col {
                width: 100%;
                margin: 0 auto;
                padding: 0;
                max-width: 324px;

                .trade-reward {
                    margin: 0 auto;
                    padding: 13px 9px 20px;
                    height: 100%;

                    .trade-reward-row {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap;
                        .lib-css(align-items, center, 1);
                        margin-bottom: 20px;

                        .trade-reward-img {
                            width: 120px;
                            .lib-vendor-prefix-display;
                            .lib-css(justify-content, center, 1);

                            img, svg {
                                max-width: 100%;
                                height: auto;
                                margin: 0 auto;
                            }
                        }

                        .trade-reward-content {
                            width: ~"calc(100% - 120px)";
                            padding-left: 15px;

                            h4 {
                                margin: 0;
                                min-height: 44px;
                                max-width: 100%;
                                justify-content: flex-start;
                                text-align: left;
                            }
                        }
                    }

                    span {
                        max-width: unset;
                        width: 100%;
                        text-align: left;
                    }

                    p {
                        max-width: 100%;
                        text-align: left;
                        margin-bottom: 16px;
                    }

                    ul {
                        max-width: 297px;
                        margin-left: 0;

                        li {
                            margin-bottom: 15px;
                        }
                    }

                    a {
                        max-width: 260px;
                    }
                }
            }
        }
    }

    .trade-rewards-promo-banner {
        margin-left: -15px;
        margin-right: -15px;
        overflow: hidden;
        background: @color-blue;

        .promo-banner-img {
            max-width: 488px;

            .promo-banner-desktop {
                display: none;
            }
        }
    }

    .register-trade-rewards-wrap {
        margin-left: -15px;
        margin-right: -15px;
        padding: 0 15px;
        overflow: hidden;

        .trade-rewards-img {
            max-width: unset !important;
            height: auto;
            left: 50%;
            transform: translateX(-50%);
            margin: 0 auto 14px;
        }

        .register-trade-rewards {
            padding: 0px 0 15px;

            h4 {
                line-height: 35px;
                margin: 0 0 8px;
            }

            p {
                margin: 0 0 13px;
            }

            a {
                .lib-heading-typography(16, 38, @color-white);
                padding: 0 28px;
                display: inline-block;
                border-radius: 8px;
            }

            .register-trade-rewards-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }
    }

    // CMS careers page 
    .careers-banner {    
        font-size: 0;
        padding-bottom: 50px;

        img {
            &.banner-desktop-img {
                display: none !important;
            }
            
            &.banner-mobile-img {
                display: block;
            }
        }

        h2 {
            .lib-heading-typography (20, 28, @color-black);
            text-align: center;
            margin-bottom: 21px;
        }

        ul {
            row-gap: 20px;
            margin: 0 -6.5px 25px;

            li {
                width: 50%;
                margin-bottom: 0;
                padding: 0 6.5px;

                a {
                    padding: 0 6px;
                    .lib-heading-typography(18, 20, @color-white);
                    min-height: 67px;
                    border-radius: 10px;
                }
            }
        }
    }

    .careers-categories {
        // padding: 15px 0 21px;

        ul {

            li {
                width: 50%;

                .category-content { 
                    padding: 15px 5px 15px;

                    .category-img {
                        margin: 0 auto 5px;
                        height: 130px;
                    }

                    span {
                        font-size: 21px;
                        line-height: 24px;
                    }
                }

                .category-content-hover {
                    padding: 10px 5px;

                    p {
                        font-size: 17px;
                        line-height: 20px;
                    }
                }
            }
        }
    }

    .our-history {
        .lib-css(flex-direction, column-reverse, 1);
        padding: 20px 0 20px;

        .our-history-content {
            width: 100%;
            padding: 0;

            h2 {
                text-align: center;
                .lib-heading-typography (20, 28, @color-black);
                margin: 0 0 8px;
            }

            h4 {
                .lib-font-size(18);
                .lib-line-height(25px);
                margin: 0 0 18px;
            }

            p {
                .lib-font-size(18);
                .lib-line-height(25px);
                margin: 0 0 24px;
            }

            a {
                padding: 11px 15px;
                display: block;
            }
        }

        .our-history-img {
            width: 100%;

            img {
                margin: -22px auto 0;
                display: block;
            }
        }

        &.metcash {
            .lib-css(flex-direction, column-reverse, 1); 
            padding: 10px 0 23px;
            border-top: 2px solid @color-gray17;

            .our-history-content {
                width: 100%;

                .metcash-img {
                    display: none;
                }
            }

            .our-history-img {
                width: 100%;
                padding-right: 0;
                margin-bottom: 14px;

                img {
                    max-width: 100%;

                    &.metcash-img {
                        display: block;
                    }
                }
            }
        }
    }

    .support-communities {
        padding: 18px 0 35px;

        h2 {
            .lib-heading-typography (20, 22, @color-black);
            max-width: 300px;
            margin: 0 auto 16px;
        }

        ul {
            .lib-css(justify-content, space-evenly, 1);

            li {
                padding-right: 0;
                gap: 16px;

                img {
                    max-width: 154px;
                    width: 100%;
                }
            }
        }
    }

    .our-team-wrap {
        padding: 23px 0 24px;

        h2 {
            .lib-heading-typography (20, 28, @color-black);
            margin: 0 0 6px;
        }

        p {
            .lib-font-size(18);
            margin: 0 0 22px;
        }

        .our-team-slider {
            padding: 0 29px;

            .our-team-slide {
                padding: 0 5px;

                .our-team-img {
                    height: 135px;
                    margin: 0 auto 8px;
                }

                span {
                    .lib-font-size(22);
                    .lib-line-height(24);
                }
            }

            .slick-arrow {
                width: 29px;
                &::before {
                    font-size: 24px;
                }
            }
        }
    }

    .members-list {
        margin-bottom: 16px;

        li {
            margin-bottom: 12px;
        }
    }

    .join-team-today {
        padding: 2px 17px 15px;
        margin: 0 -15px;

        .join-team-today-inner {
            .lib-css(justify-content, center, 1);

            img {
                width: 108px;
                height: 112px;
            }

            .join-team-today-content {
                width: 100%;
                padding-left: 0;
                padding-top: 0;
                text-align: center;

                h2 {
                    .lib-heading-typography (26, 30, @color-white);
                    max-width: 340px;
                    margin: 0 auto 11px;
                }

                a {
                    .lib-heading-typography (26, 32, @color-white);
                    padding: 4.5px 46px;
                }
            }
        }
    }

    // Add styling for google Review on PDP page 
    .catalog-product-view {
        #___ratingbadge_0 {
            z-index: 18 !important;
        }
    }

    // CMS AfterPay page
    .afterpay-banner {
        margin: 0 -15px;

        img {
            &.afterpay-banner-desktop {
                display: none !important;
            }

            &.afterpay-banner-mobile {
                display: block;
            }
        }
    }

    .afterpay-sec {
        padding: 40px 0;
        
        .intro-row {
            .intro-col {
                .intro-card {
                    gap: 16px;

                    span, h4 {
                        font-size: 18px;
                    }
                }
            }
        }

        .why-use-row {
            .why-use-col {
                .why-use-card {
                    gap: 16px;

                    h4 {
                        font-size: 18px;
                    }
                }
            }
        }
    }

    // Add styling for Chatbot
    .catalog-product-view {
        #launcher {
            bottom: 153px !important;
        }
        
        div {
            &:has(> div > iframe[title="Close message"]){
                bottom: 224px !important;
            }
        }
    }

    .checkout-index-index {
        #launcher {
            bottom: 125px !important;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .page-footer {
        .footer-middle {
            .footer_middle_left {
                p {
                    a {
                        .lib-font-size(15);
                        .lib-css(letter-spacing, 0.05px, 1);
                    }
                }
            }

            .footer_middle_right {
                p {
                    .lib-font-size(15);
                    .lib-css(letter-spacing, 0.05px, 1);
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-footer {
        .footer-middle {
            background: @secondary__color;

            .footer {
                &.content {
                    margin-left: auto;
                    margin-right: auto;
                    max-width: 1282px;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, space-between, 1);
                    padding: 16px 22px;
                }
            }
        }

        .footer-bottom {
            padding-top: 25px;

            .footer {
                &.content {
                    margin-left: auto;
                    margin-right: auto;
                    max-width: 1282px;
                    padding: 10px 20px 17px;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, flex-start, 1);
                }
            }

            .footer_customer_care,
            .footer_links_general,
            .footer_links_right,
            .footer_join_community {
                width: 25%;
            }

            .footer_customer_care,
            .footer_links_general,
            .footer_links_right {
                .block-content {
                    display: block !important;
                    height: auto !important;
                }
            }

            h2 {
                padding: 0;
                margin: 0 0 12px;
            }

            .links {
                margin: 0 0 20px;
                padding: 0 50px 0 0;
            }

            .socials {
                padding: 0 0 15px;

                li {
                    margin-right: 5px;

                    a {
                        width: 35px;
                        height: 35px;

                        svg {
                            width: 20px;
                            height: 20px;
                        }
                    }
                }
            }
        }

        .footer-copyright {
            .footer {
                &.content {
                    margin-left: auto;
                    margin-right: auto;
                    max-width: 1282px;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, space-between, 1);
                    padding: 8px 3px 8px 165px;
                    position: relative;
                    min-height: 70px;

                    &::before {
                        content: '';
                        width: 142px;
                        height: 109px;
                        position: absolute;
                        left: 20px;
                        top: -29px;
                        z-index: 1;
                        background: url(../images/footer-logo.png) no-repeat;
                    }
                }
            }

            .copyright {
                .lib-font-size(14);
                .lib-line-height(17.5);
                color: @color-white;
                font-family: @font-family__base;
                padding: 10px 0;
            }

            .payments-wrapper {
                margin-left: auto;

                p {
                    .lib-font-size(14);
                    .lib-line-height(34);
                    color: @color-white;
                    font-family: @font-family__base;
                    margin: 0 4px 0 0;
                    display: none;
                }
            }
        }
    }

    .home-top-banner {
        .homepage-boxes {
            position: relative;
            margin-bottom: -15.9%;

            .homepage-boxes-list {
                .homepage-box {
                    overflow: hidden;

                    .homepage-box-link {
                        width: 100%;
                    }

                    .homepage-box-image {
                        width: 195%;
                        height: auto;
                        max-width: unset;
                        left: 50%;
                        transform: translateX(-50%);
                        position: relative;
                    }
                }

                .slick-arrow {
                    width: 42px;
                    height: 42px;
                    top: ~"calc(50% - 15.9% - 21px)";

                    &.slick-prev {
                        left: 20px;
                    }

                    &.slick-next {
                        right: 20px;
                    }
                }

                .slick-dots {
                    left: 50%;
                    top: 56%;
                    align-items: center;
                    justify-content: flex-start;
                    position: absolute;
                    width: ~"calc(100vw - 40px)";
                    padding: 0 15px;
                    transform: translate(-50%, -50%);
                    z-index: 5;

                    li {
                        width: auto;
                        height: auto;
                        margin: 0 10px 0 0;

                        button {
                            width: 25px;
                            height: 6px;
                            border: 0;
                        }
                    }
                }
            }
        }
    }

    .homepage-top-brands {
        margin: 0 auto;
        max-width: 1280px;
        padding: 0;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;

        .view-more {
            display: none;
        }
    }

    .subcategories-wrapper {
        h2 {
            .lib-font-size(30);
            margin: 0 0 50px;
        }

        .subcategories-list {
            .item {
                width: 16.667%;
            }
        }
    }

    .homepage-block {
        .pagebuilder-column-group {
            .pagebuilder-column-line {
                .pagebuilder-column {

                    .pagebuilder-mobile-only {
                        display: none !important;
                    }

                    &:first-child {
                        width: 20% !important;
                        padding-right: 10px;
                    }

                    &:last-child {
                        width: 80% !important;
                    }
                }
            }
        }
    }

    #add-cart-notifications {
        &.top-right {
            top: 10%;
            right: 15%;
        }

        &.top-left {
            top: 10%;
            left: 15%;
        }

        &.bottom-right {
            bottom: 10%;
            right: 15%;
        }

        &.bottom-left {
            bottom: 10%;
            left: 15%;
        }
    }

    // 404 page CSS
    .cms-no-route {
        .columns .column.main {
            background: url(../images/hammer.jpg) no-repeat right 82px;
            padding-bottom: 20px;

            ul {
                min-height: 250px;
            }
        }
    }

    // CMS business reward page
    .cms-trade-rewards {
        .page-wrapper {
            overflow: hidden;
        }
    }

    .trade-rewards {
        position: relative;

        img {
            &.trade-rewards-desktop {
                display: block;
                width: 204%;
                height: auto;
                max-width: unset;
                left: 50%;
                transform: translateX(-50%);
                position: relative;
            }

            &.trade-rewards-mobile {
                display: none !important;
            }
        }

        a {
            .lib-heading-typography(22, 50, @secondary__color);
            border: 4px solid @secondary__color;
            padding: 0 20px;
            right: 0;
            bottom: 22px;
        }
    }

    .rewards-card-wrap {
        padding: 55px 0 46px;

        h2 {
            .lib-heading-typography(33, 47, @secondary__color);
            margin: 0 0 42px;
        }

        .rewards-row {
            row-gap: 25px;
            margin-bottom: 0;

            .rewards-col {
                width: 50%;

                .trade-reward {
                    position: relative;
                    padding-bottom: 77px;
                    height: 100%;

                    .trade-reward-row {
                        .trade-reward-content {
                            h4 {
                                display: block;
                                margin-bottom: 25px
                            }
                        }
                    }

                    p {
                        .lib-line-height(18.5);
                        min-height: 55px;
                    }

                    a {
                        position: absolute;
                        width: ~"calc(100% - 18px)";
                        left: 9px;
                        bottom: 20px;
                    }
                }
            }
        }
    }

    .trade-rewards-promo-banner {

        &::before {
            content: "";
            background: @color-blue;
            width: 100vw;
            height: 100%;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            position: absolute;
        }

        .promo-banner-img {
            max-width: 1031px;

            .promo-banner-mobile {
                display: none;
            }
        }
    }

    .register-trade-rewards-wrap {
        padding: 0 0 19px;

        &::before {
            content: "";
            background: @secondary__color;
            width: 100vw;
            height: 100%;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            position: absolute;
        }

        .trade-rewards-img {
            max-height: 100%;
            margin: 0 auto 14px;
            display: block;
        }

        .register-trade-rewards {
            a {
                .lib-heading-typography(26, 55, @color-white);
                display: block;
                padding: 0 8px;
                max-width: 413px;
            }
        }
    }

    // CMS Delivery page
    div[data-content-type="tabs"] {
        .tabs-navigation {
            li {
                &.tab-header {
                    &.ui-tabs-tab {
                        a {
                            font-size: 16px !important;
                        }
                    }
                }
            }
        }

        &.custom-tabs {
            padding: 0 20px;

            .tabs-navigation {
                li {
                    width: auto;

                    &.tab-header {
                        &.ui-tabs-tab {
                            a {
                                padding: 17px 20px 14px;
                                font-weight: 900 !important;
                                text-transform: uppercase !important;

                                span {
                                    max-width: 100%;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.delivery-tabs {
            padding: 0 20px;

            .tabs-navigation {
                li {
                    width: auto;

                    &.tab-header {
                        &.ui-tabs-tab {
                            a {
                                padding: 18px 20px 14px;
                                font-weight: 900 !important;
                                text-transform: uppercase !important;

                                span {
                                    max-width: 100%;
                                }
                            }

                            &.click-collect,
                            &.free-delivery,
                            &.standard-delivery,
                            &.uber-delivery {
                                a {
                                    &::before {
                                        margin-bottom: 8px;
                                    }
                                }
                            }

                            &.express-delivery {
                                a {
                                    &::before {
                                        margin-bottom: 8px;
                                    }

                                    &::after {
                                        top: 12% !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // CMS careers page 
    .careers-banner {
        ul {
            margin-bottom: 38px;
            li {
                width: 50%;
                margin-bottom: 14px;
                a {
                    padding: 0 15px;
                    .lib-heading-typography(26, 30, @color-white);
                }
            }
        }
    }

    .careers-categories {   
        ul {
            li {
                width: 50%;
                .category-content {
                    padding-top: 10px;
                    padding-bottom: 16px;

                    .category-img {
                        margin: 0 auto 10px;
                        height: 200px;
                    }
                    span {
                        max-width: 280px;
                        margin: 0 auto;
                    }
                }

                .category-content-hover {
                    padding: 20px 20px;

                    p {
                        font-size: 22px;
                        line-height: 25px;
                    }
                }
            }
        }
    }

    .our-history {
        .our-history-content {
            padding-right: 20px;
        }
    }

    .support-communities {
        ul {
            li {
                padding-right: 80px;
                img {
                    max-width: 250px;
                }
            }
        }
    }

    .our-team-wrap {
        .our-team-slider {
            padding: 0 80px;
            .our-team-slide {
                padding: 0 10px;
            }
        }
    }

    .members-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 47px;

        li {
            width: 33.33%;
            margin-bottom: 0;
        }
    }

    .join-team-today {
        padding: 14px 0 0 10px;
        .join-team-today-inner {
            max-width: 630px;
            margin: 0 auto;
            img {
                width: 180px;
            }
            .join-team-today-content {
                width: ~"calc(100% - 180px)";
                padding-left: 30px;
                padding-top: 30px;
                h2 {
                    .lib-heading-typography (35, 42, @color-white);
                }
                a {
                    .lib-heading-typography (26, 32, @color-white);
                    padding: 8px 24px;
                }
            }
        }
    }

    // Blog Section
    .blog-wrapper {
        display: block;
    }

    // CMS AfterPay page
    .afterpay-banner {
        position: relative;
        img {
            &.afterpay-banner-desktop {
                width: 100%;
            }
            &.afterpay-banner-mobile {
                display: none !important;
            }
        }
    }
    
    .afterpay-sec {
        padding: 50px 0;
        
        .intro-row {
            align-items: stretch;
            .intro-col {
                width: 50%;
                margin: 0 auto;
                .intro-card {
                    gap: 20px;
                    span, h4 {
                        font-size: 20px;
                        line-height: 1.3;
                        font-weight: 700;
                    }
                }
            }
        }
        
        .why-use-row {
            align-items: stretch;
            .why-use-col {
                width: 50%;
                margin: 0 auto;
                .why-use-card {
                    gap: 20px;
                    h4 {
                        font-size: 20px;
                        line-height: 1.3;
                        font-weight: 700;
                    }
                }
            }
        }
        .for-setup {
            ul {
                column-count: 2;
                column-gap: 30px;
            }
        }
    }
    
    .account-setup-sec {
        & > ul {
            li {
                font-size: 18px;
                line-height: 24px;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__l) {
    .page-footer {
        .footer-bottom {
            .socials {
                padding: 18px 0 23px;

                li {
                    margin-right: 10px;

                    a {
                        width: 44px;
                        height: 44px;

                        svg {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }
            }
        }

        .footer-copyright {
            .copyright {
                width: 35%;
            }

            .payments-wrapper {
                p {
                    display: block;
                }
            }
        }
    }

    .home-top-banner {
        .homepage-boxes {
            .homepage-boxes-list {
                .homepage-box {
                    .homepage-box-image {
                        width: 197%;
                    }
                }

                .slick-dots {
                    width: ~"calc(100vw - 50px)";
                    top: 56.5%;
                    max-width: 1240px;

                    li {
                        button {
                            width: 30px;
                            height: 6px;
                            border: 0;
                        }
                    }
                }
            }
        }
    }

    // CMS business reward page
    .trade-rewards {
        a {
            .lib-font-size(26);
            .lib-line-height(58);
            padding: 0 26px;
        }
    }

    .rewards-card-wrap {
        .rewards-row {
            margin-left: -10px;
            margin-right: -10px;

            .rewards-col {
                width: 25%;
                padding: 0 10px;

                .trade-reward {
                    a {
                        .lib-font-size(14);
                        padding-right: 28px;

                        &::after {
                            top: ~"calc(50% - 9px)";
                            right: 16px;
                            .lib-font-size(18);
                        }
                    }
                }
            }
        }
    }

    .register-trade-rewards-wrap {
        padding: 18px 0 19px;

        .trade-rewards-img {
            position: absolute;
            left: -103px;
            z-index: 1;
            top: 0;
            margin: 0;
        }

        .register-trade-rewards {
            padding-left: 425px;
        }
    }

    // CMS careers page 
    .careers-banner {
        ul {
            margin-bottom: 52px;
            li {
                width: 25%;
                margin-bottom: 0;
            }
        }
    }

    .careers-categories {
        ul {
            li {
                width: 25%;
                span {
                    max-width: 100%;
                }
            }
        }
    }

    .our-history {
        .our-history-content {
            padding-right: 32px;
        }
    }

    .support-communities {
        ul {
            li {
                padding-right: 100px;
                img {
                    max-width: 300px;
                }
            }
        }
    }

    .our-team-wrap {
        .our-team-slider {
            padding: 0 100px;
        }
    }

    .join-team-today {
        padding: 14px 0 0 15px;
        .join-team-today-inner {
            max-width: 800px;
            img {
                width: 220px;
            }
            .join-team-today-content {
                width: ~"calc(100% - 220px)";
                padding-left: 45px;
                padding-top: 32px;
                h2 {
                    .lib-heading-typography (42, 50, @color-white);
                }
                a {
                    .lib-heading-typography (28, 32, @color-white);
                    padding: 10px 28px;
                }
            }
        }
    }

    // CMS AfterPay page
    .intro-sec {
        .intro-row {
            .intro-col {
                width: 33.33%;
            }
        }
    }
    
    .why-use-sec {
        .why-use-row {
            .why-use-col {
                width: 25%;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__ml) {
    .page-footer {
        .footer-middle {
            min-height: 54px;
        }

        .footer-bottom {
            min-height: 472px;
        }

        .footer-copyright {
            min-height: 70px;
        }
    }

    .home-top-banner {
        .homepage-boxes {
            margin-bottom: -207px;
            min-height: 207px;

            .homepage-boxes-list {
                .homepage-box {
                    .homepage-box-image {
                        width: 2540px;
                    }
                }

                .slick-arrow {
                    top: ~"calc(50% - 103.5px - 21px)";
                }

                .slick-arrow {

                    &.slick-prev {
                        left: ~"calc(50% - 600px)";
                        transform: translateX(-50%);
                    }

                    &.slick-next {
                        left: ~"calc(50% + 600px)";
                        right: auto;
                        transform: translateX(-50%);
                    }
                }
            }
        }
    }

    // CMS business reward page
    .trade-rewards {
        img {
            &.trade-rewards-desktop {
                width: 2540px;
            }
        }
    }

    .rewards-card-wrap {
        .rewards-row {
            margin-left: -12.5px;
            margin-right: -12.5px;

            .rewards-col {
                padding: 0 12.5px;

                .trade-reward {
                    a {
                        .lib-font-size(15);
                        padding-right: 38px;

                        &::after {
                            top: ~"calc(50% - 10px)";
                            right: 20px;
                            .lib-font-size(20);
                        }
                    }
                }
            }
        }
    }

    .register-trade-rewards-wrap {
        .register-trade-rewards {
            padding-right: 148px;
        }
    }

    // CMS careers page

    .support-communities {
        ul {
            li {
                padding-right: 123px;
                img {
                    max-width: 351px;
                }
            }
        }
    }

    .our-team-wrap {
        .our-team-slider {
            padding: 0 145px;
        }
    }

    .join-team-today {
        padding: 14px 0px 0 25px;
        .join-team-today-inner {
            max-width: 960px;
            img {
                width: 243px;
            }
            .join-team-today-content {
                width: ~"calc(100% - 243px)";
                padding-left: 61px;
                padding-top: 34px;
                h2 {
                    .lib-heading-typography (49, 55, @color-white);
                }
                a {
                    .lib-heading-typography (30, 32, @color-white);
                    padding: 12px 32px;
                }
            }
        }
    }
}
