<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
?>
<?php /** @var $block \Magento\Checkout\Block\Onepage */ ?>
<a href="javascript:void(0)" class="back-btn" id="checkout-back-button" style="display: none;">Back</a>
<script type="text/javascript">
    require(['jquery'], function($) {
        $(document).ready(function(){
            var backButton = $('#checkout-back-button');
            
            // Store original referrer on first visit
            if (document.referrer && document.referrer.indexOf('checkout') === -1) {
                sessionStorage.setItem('originalReferrer', document.referrer);
                backButton.attr('href', document.referrer).show();
            } else {
                // Use stored referrer on reloads
                var storedReferrer = sessionStorage.getItem('originalReferrer');
                if (storedReferrer) {
                    backButton.attr('href', storedReferrer).show();
                } else {
                    backButton.hide();
                }
            }
        });
    });
</script>