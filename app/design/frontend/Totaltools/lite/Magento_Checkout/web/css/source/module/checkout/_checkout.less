// /**
//  * Copyright © 2016 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@checkout-wrapper__margin: @indent__base;
@checkout-wrapper__columns: 8;
@checkout-step-title__border: @border-width__base solid @color-gray80;
@checkout-step-title__font-size: 26px;
@checkout-step-title__font-weight: @font-weight__light;
@checkout-step-title__padding: @indent__s;
@checkout-step-title-mobile__font-size: 18px;
@indent__m: @indent__base - 5;

._checkout-block-title {
	margin: 0;
	padding: 0 0 8px 0;
	width: 100%;
	display: block;
	.lib-font-size(24);
	.lib-line-height(24);
	.lib-css(color, @heading__color__base);
	.lib-css(font-family, @font-family__heading);
	.lib-css(font-weight, @checkout-step-title__font-weight);
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
	.checkout-index-index {
		.page-header {
			position: relative;
			margin-bottom: 0;

			.header {
				.logo {
					margin: 0 auto;
				}
			}

			.back-btn {
				font-weight: 700;
				color: @color-white;
				display: inline-flex;
				align-items: center;
				text-decoration: unset;
				gap: 10px;
				position: absolute;
				transform: translateY(-50%);
				z-index: 1;

				&::before {
					content: '\E829';
					display: block;
					font-family: 'TT Icons';
					color: @color-white;
					font-weight: 400;
					font-family: @font-tt-icons;
				}
			}
		}
	}

	.captcha-image {
		.lib-clearfix();
		margin: 10px 0;

		img {
			float: left;
		}

		button {
			float: left;
			margin: 10px 0 0 10px;
			.lib-font-size(14);
		}
	}
	.address_validation_msg {
		margin: 0 0 10px;
		padding: 18px 20px 14px;
		display: block;
		line-height: 1em;
		font-size: 1.5rem;
		background: #fac7c6;
		color: #E41B13;
		padding-left: 56px;
		position: relative;

		&:before {
			-webkit-font-smoothing: antialiased;
			font-size: 20px;
			line-height: 20px;
			color: #b30000;
			content: '\e80a';
			font-family: @font-tt-icons;
			margin: -10px 0 0;
			vertical-align: middle;
			display: inline-block;
			font-weight: normal;
			overflow: hidden;
			speak: none;
			left: 8px;
			top: 25px;
			width: 56px;
			position: absolute;
			text-align: center;
		}
	}

	.checkout-cart-index .page-main,
	.checkout-index-index,
    .checkout-steps-index {
		#checkout,
		.checkout-container {
			margin: 0 !important;
		}

		.opc-wrapper-layout {
			.lib-css(width, 100%);
			.lib-css(margin, 0 auto 70px);
			.lib-css(clear, both);
			.lib-clearfix();

			.opc-wrapper {
				.lib-css(width, 100%);
				.lib-css(display, inline-block);
				.lib-css(float, left);
				.lib-css(box-sizing, border-box);
				.lib-css(padding, 0);
			}

			.sidebar-wrapper {
				.lib-css(width, 100%);
				.lib-css(display, inline-block);
				.lib-css(float, right);
				.lib-css(box-sizing, border-box);

				.sidebar-container {
					.lib-css(width, 100%);

					> .sidebar-aside-wrapper {
						.lib-css(margin-bottom, @indent__m + 2);
					}

					> .actions-toolbar {
						.lib-css(margin-bottom, @indent__m + 3);
					}

					&.affixed {
						position: fixed;
						z-index: 9999999;
						top: 0;
					}
				}
			}
		}

		.field {
			.lib-clearfix();

			input[type='text'],
			input[type='password'],
			input[type='url'],
			input[type='tel'],
			input[type='search'],
			input[type='number'],
			input[type='datetime'],
			input[type='email'],
			select {
				height: 4.2rem;
				.lib-line-height(42);
			}

			legend {
				border: none;
			}
		}

		.heading,
		.title {
			&.required,
			&._required {
				&:after {
					content: '*';
					margin-left: 2px;
					.lib-css(color, @color-red2);
				}
			}
		}

		#shipping-new-address-form {
			.street legend:after {
				display: none !important;
			}
		}

		.continue-shopping-top-wrapper {
			margin: 0 0 0 auto;
			padding: 0;
			.lib-vendor-prefix-order(4);

			li {
				padding: 0;
				margin: 0;
				list-style: none;
				color: @color-white;

				.lib-icon-font(
					@_icon-font-content: @tt-icon-lock,
					@_icon-font-size: 36px,
					@_icon-font-line-height: 30px,
					@_icon-font-position:before,
					@_icon-font-color: rgba(255, 255, 255, 0.75),
					@_icon-font-margin:0 4px 0 0,
					@_icon-font-vertical-align:top
				);

				span {
					display: inline-block;
					width: 60px;
					font-family: @font-family__base;
					font-size: 14px;
					letter-spacing: 0.2px;
					.lib-line-height(15);
					color: @color-white;
				}
			}
		}

		fieldset {
			&.street {
				legend {
					display: none !important;
				}
			}
		}

		> .loading-mask {
			z-index: 99999999;
			background: rgba(0, 0, 0, 0.4);

			.loader-content {
				width: 320px;
				position: absolute;
				top: 50%;
				left: 50%;
				margin-left: -160px;
				-webkit-transform: translate(0, -50%);
				-moz-transform: translate(0, -50%);
				-ms-transform: translate(0, -50%);
				-o-transform: translate(0, -50%);
				transform: translate(0, -50%);
				background: @color-white;
				padding: 25px 25px 40px;
				text-align: center;
				-webkit-box-sizing: border-box;
				-moz-box-sizing: border-box;
				box-sizing: border-box;

				> p {
					padding-bottom: 10px;
					margin: 0 0 20px;
					border-bottom: 1px solid #ccc;
				}
				img {
					display: block;
					margin: 0 auto;
					width: 30px;
				}
			}
		}

		#checkout-loader {
			background-color: rgba(0, 0, 0, 0.4);
			z-index: 99999999;
		}

		.actions-toolbar {
			.is-loading {
				pointer-events: none;
				.lib-css(opacity, 0.5, 1);
			}

			.button-mobile {
				display: none;
			}
		}
	}

	.checkout-index-index {
		.billing-address-form,
		.form-shipping-address {
			fieldset {
				&.street {
					legend {
						display: block !important;
						padding: 0;
						position: relative;
					}

					.control {
						.loqate-address {
							& + .field {
								.label {
									display: none;
								}
							}
						}
					}
				}
			}
		}

		.form-shipping-address {
			fieldset {
				&.street {
					legend {
						span {
							&::after {
								content: '*';
								color: @color-red6;
								margin-left: 2px;
							}
						}
					}
				}
			}
		}
		
		.fieldset {
			.field {
				.field-error {
					margin-top: 5px;
				}
			}
		}

		.amcheckout-block {
			.discount-code {
				.form-discount {
					align-items: flex-start;	
				}
			}
		}

		.shipping-address-fields {
			.step-title {
				&::after {
					content: '*';
					margin-left: 2px;
				}
			}

			fieldset {
				&.street {
					.control {
						._required {
							.label {
								span {
									&::after {
										content: '*';
										color: @color-red6;
										margin-left: 2px;
									}
								}
							}
						}
					}
				}
			}

			.address-form-fields {
				margin-bottom: 20px;
			}
		}

		.billing-address-form {
			.loqate-address { 
				margin-top: 10px;
			}
		}
		
		.amcheckout-block {
			.discount-code {
				&.opc-payment-additional {
					margin-top: 15px;
				}
			}
		}
	}

	.checkout-onepage-success {
		.page-title-wrapper {
			.lib-vendor-prefix-display();
			.lib-vendor-box-align(flex-start);
			.lib-css(margin-top, @indent__base);

			h1 {
				.lib-line-height(32);
			}

			.print {
				white-space: nowrap;
				.lib-button-outline();
				.lib-css(margin-left, auto);
			}
		}

		.actions-toolbar,
		.thank-you {
			margin: @indent__base 0;
		}

		.note {
			margin: 0;

			span {
				font-weight: bold;
				color: @color-blue;
			}
		}

		.checkout-success-wrapper {
			.lib-clearfix();

			.checkout-success {
				.lib-css(box-sizing, border-box);
				margin-bottom: 25px;
			}

			.success-block {
				.lib-css(box-sizing, border-box);

				.balance-box-container {
					position: relative;
					z-index: 1;
				}

				.balance-box {
					padding: 30px 70px 30px 30px;
					border: 1px solid #cccccc;
					border-radius: 3px;
					.lib-background-gradient(
						@_background-gradient: true,
						@_background-gradient-direction:vertical,
						@_background-gradient-color-start:#fff,
						@_background-gradient-color-end:#ebebeb
					);
					position: relative;
					min-height: 120px;
					margin-top: 22px;

					img {
						position: absolute;
						right: 0;
						bottom: 0;
					}

					.balance-box-right_franchise {
						img {
							right: 5px;
						}
					}

					.balance-box-heading {
						text-transform: uppercase;
						color: @color-blue;
						margin: 0 0 15px;
						font-size: 24px;
						line-height: 1.1;
					}

					.balance-box-content {
						margin-bottom: 10px;
					}

					.balance-box-button {
						text-decoration: none;
						.lib-button(
							@_button-font-family: @font-family__base,
							@_button-font-size:14px,
							@_button-line-height:14px,
							@_button-padding:11px 14px 8px,
							@_button-background:@color-blue,
							@_button-border:2px solid #173f91,
							@_button-color:#ffffff
						);
						text-transform: uppercase;

						&:hover {
							background: @color-blue;
							border: 2px solid #173f91;
							color: #fff;
						}
					}
				}
			}

			.registration-block {
				p {
					padding: 0;
					margin: 0;
					line-height: 24px;
				}

				.action {
					&.primary {
						margin-top: 20px !important;
						.lib-button(
							@_button-font-family: @font-family__base,
							@_button-font-size:14px,
							@_button-line-height:14px,
							@_button-padding:11px 20px 8px,
							@_button-background:@color-blue,
							@_button-border:2px solid #173f91,
							@_button-color:#ffffff
						);
						text-transform: uppercase;
						border-radius: 3px;

						&:hover,
						&:focus,
						&:active {
							background: #0058a6;
							border: 2px solid #173f91;
							color: #fff;
						}
					}
				}
			}
		}
	}

	.checkout-cart-index,
	.checkout-index-index,
    .checkout-steps-index,
	.checkout-onepage-success {
		.page-title-wrapper {
			&:extend(.abs-visually-hidden all);
		}
	}

	.checkout-container {
		&:extend(.abs-add-clearfix all);
		.lib-css(margin, 0 0 @checkout-wrapper__margin);
	}

	.opc-wrapper {
		.lib-css(margin, 0 0 @checkout-wrapper__margin);

		.opc {
			&:extend(.abs-reset-list all);
		}

		.step-title {
			font-family: @font-family__base;
			.lib-font-size(24px);
			.lib-line-height(30px);
			font-weight: 700;
			margin: 0 0 @indent__s 0;
			letter-spacing: 0;
			color: @color-gray20;
		}

		.step-content {
			margin: 0;
		}

		label,
		input,
		select,
		textarea {
			font-family: @font-family__base;
		}

		// #checkoutSteps {
		//     #lib-layout-columns();
		//     .lib-css(box-sizing, border-box);

		//     #shipping {
		//         .lib-layout-column(2, 2, 50%);
		//         .lib-css(box-sizing, border-box);
		//         padding: 0 25px 0 0;
		//     }

		//     #opc-shipping_method {
		//         .lib-layout-column(2, 2, 50%);
		//         .lib-css(box-sizing, border-box);
		//         padding: 0 0 0 25px;

		//         .table-checkout-shipping-method {
		//             min-width: 100%;
		//         }

		//     }

		//     #payment {
		//         #lib-layout-columns > .main();
		//     }

		// }
	}

	.checkout-cart-index,
	.checkout-index-index,
    .checkout-steps-index {
		.nav-sections,
		.nav-toggle {
			display: none !important;
		}

		.logo {
			margin-left: 0;
		}

		#checkout {
			margin-top: 30px;
		}
	}

	.tooltip-left {
		.lib-tooltip(left);
	}

	.tooltip-right {
		.lib-tooltip(right);
	}

	.tooltip-top {
		.lib-tooltip(top);
	}

	.tooltip-bottom {
		.lib-tooltip(bottom);
	}

	.checkout-tooltip {
		.tooltip-toggle {
			.lib-icon-font(
				@_icon-font-content: @tt-icon-info,
				@_icon-font-size: 16px,
				@_icon-font-margin: 0 1px,
				@_icon-font-color: @color-blue,
				@_icon-font-vertical-align: top,
				@_icon-font-position: before,
				@_icon-font-text-hide: true
			);
		}

		.tooltip-content {
			.lib-font-size(14);
		}
	}

	@media screen and (min-width: 768px) and (max-width: 960px) {
		.opc-wrapper .step-title {
			.lib-font-size(22);
		}
	}

	div[name="shippingAddress.street.0"],
	div[name="shippingAddressshared.street.0"],
	div[name="billingAddressshared.street.0"],
	div[name="billingAddress.street.0"] {
		.field-note {
			.lib-css(color, @color-gray40);
			.lib-font-size(12);
		}
	}
}

//
//  Tablet
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__m) {
	.checkout-index-index,
    .checkout-steps-index {
		.continue-shopping-top-wrapper {
			margin-top: 15px;
		}

		.opc-wrapper-layout {
			.sidebar-wrapper {
				position: static;
			}
		}
	}

	.page-header {
		.back-btn {
			font-size: 20px;
			line-height: 1.2;
			left: 20px;
			top: 50%;

			&::before {
				font-size: 15px;
			}
		}
	}
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__l) {
	.checkout-cart-index,
	.checkout-index-index,
    .checkout-steps-index {
		.opc-wrapper-layout {
			.opc-wrapper {
				.lib-css(width, 67.5%);
			}

			.sidebar-wrapper {
				.lib-css(width, 27.5%);
				.lib-css(position, relative);

				&.relative {
					position: relative;
				}

				&.static {
					position: static;
				}
			}
		}

		#checkout-step-shipping form .actions-toolbar {
			margin-left: 0;
		}
	}
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__ml) {
	.checkout-index-index {
		.page-header {
			.back-btn {
				left: ~"calc(50% - 620px)";
			}
		}
	}
}

// Custom breakpoint for fix width opc-wrapper and opc-sidebar
@media screen and (min-width: 1200px) {
	.checkout-index-index,
    .checkout-steps-index {
		.opc-wrapper-layout {
			.opc-wrapper {
				.lib-css(width, 836px);
			}

			.sidebar-wrapper {
				.lib-css(width, 341px);
			}
		}
	}
}

@media screen and (min-width: 1024px) and (max-width: 1242px) {
	.checkout-index-index,
    .checkout-steps-index {
		.opc-wrapper-layout {
			.sidebar-wrapper {
				&.affixed {
					right: @indent__base;
				}
			}
		}
	}
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.checkout-cart-index,
	.checkout-index-index,
    .checkout-steps-index {
		.opc-progress-bar {
			.lib-css(margin, 30px 0 25px);
		}

		.opc-wrapper-layout {
			.lib-css(margin, 0 auto);

			.opc-wrapper {
				.lib-css(width, 100%);
				.lib-css(display, block);
				.lib-css(float, none);
				.lib-clearfix();

				.step-content {
					margin: 0;
				}
			}

			.sidebar-wrapper {
				.lib-css(width, 100%);
				.lib-css(display, block);
				.lib-css(float, none);
				.lib-css(position, static);
				.lib-clearfix();
			}
		}

		// Continue shopping wrapper
		.continue-shopping-top-wrapper {
			li:before {
				.lib-font-size(30);
			}
		}

		.actions-toolbar {
			.button-mobile {
				display: block;
			}

			.button-fixed {
				position: fixed;
				z-index: 10;
				width: 100%;
				bottom: 0;
				left: 0;
				margin: 0;
				.lib-css(border-radius, 0, 1) !important;
			}
		}

		.page-footer {
			.payments-wrapper {
				display: none;
			}
		}
	}

	.checkout-index-index {
		#___ratingbadge_0 {
			display: none !important;
		}
	}

	.page-header {
		.back-btn {
			font-size: 16px;
			line-height: 1.25;
			top: ~"calc(50% + 2px)";
			left: 15px;

			&::before {
				font-size: 12px;    
			}
		}
	}
}

//
//  Mobile SM
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break = @screen__s) {
	.opc-wrapper-layout {
		.opc-wrapper {
			.step-title {
				// .lib-css(font-size, @checkout-step-title-mobile__font-size);
				border-bottom: 0;
				padding-bottom: 0;
			}
		}
	}
}

//
//  Mobile XXS
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break = @screen__xxs) {
	.checkout-index-index {
		.page-header {
			.header {
				.secure-checkout-wrapper {
					display: none;
				}
			}
		}
	}
}

& when (@media-common = true) {
	@media screen and (max-width: 1023px) {
		.checkout-index-index {
			.amcheckout-main-container.-modern.-layout-3columns {
				.lib-vendor-prefix-display;
				.lib-vendor-prefix-flex-direction(column);
				width: 100%;
			}
		}
	}
}