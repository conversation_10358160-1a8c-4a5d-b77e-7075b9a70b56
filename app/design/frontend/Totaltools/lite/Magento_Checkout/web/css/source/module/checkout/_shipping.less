// /**
//  * Copyright © 2016 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@checkout-form-desktop__width: 390px;
@checkout-form-input__height: 40px;
@checkout-shipping-address__max-width: 48%;
@checkout-shipping-address__margin-top: 10px;
@checkout-shipping-item-icon__background-color: @color-blue;
@checkout-shipping-item-icon__color: @color-white;
@checkout-shipping-item-icon__content: @tt-icon-check-bold;
@checkout-shipping-item__border: 2px solid #ccc;
@checkout-shipping-item__line-height: 24;
@checkout-shipping-item__margin: 0 0 @indent__m 0;
@checkout-shipping-item__padding: 25px;
@checkout-shipping-item__transition: all 0.3s ease;
@checkout-shipping-item__width: 100%/3;
@checkout-shipping-item-tablet__width: 100%/2;
@checkout-shipping-item-mobile__width: 100%;
@checkout-shipping-item__active__border-color: @color-blue;
@checkout-shipping-item-icon__selected__height: 36px;
@checkout-shipping-item-icon__selected__width: 36px;
@checkout-shipping-item-mobile__padding: 0 10px;
@checkout-shipping-item-mobile__margin: 7px 0;
@checkout-shipping-item-mobile__active__padding: 15px (@indent__l + 5px) 15px 18px;
@checkout-shipping-item-before__border-color: #ccc;
@checkout-shipping-item-before__height: calc(~'100% - 20px');
@checkout-shipping-method__border: @checkout-step-title__border;
@checkout-shipping-method__padding: @indent__base;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
	.checkout-container,
	.opc-wrapper {
		.checkout-shipping-address {
			.lib-layout-column(1, 1, 100%);
			.lib-css(box-sizing, border-box);
			.lib-clearfix();
			font-family: @font-family__base;

			.step-title {
				.lib-css(font-weight, @font-weight__extrabold);
				.lib-css(color, @color-blue);
				.lib-font-size(18);
			}

			.step-content {
				.lib-layout-column(1, 1, 100%);
				border: 1px solid @color-gray4;
				padding: @checkout-shipping-item__padding;
				.lib-css(box-sizing, border-box);
				position: relative;
			}
		}

		.field.choice{
			.lib-css(margin-bottom, @indent__base);
		}

		.heading {
			.lib-css(font-weight, @font-weight__extrabold);
			.lib-css(color, @color-blue);
			.lib-css(margin, @indent__m 0 @indent__m);
			.lib-css(letter-spacing, 0.2px);
			.lib-font-size(18);
			.lib-line-height(18);
			counter-increment: title;

			&.required {
				&:after {
					.lib-css(content, '*');
					.lib-css(margin-left, 2px);
					.lib-css(color, @color-red6);
				}
			}

			&:before {
				content: counter(title) '. ';
			}
		}

		//
		//  Insider Rewards
		//  ---------------------------------------------
		.insider-rewards {
			width: 100%;
			.lib-css(margin-top, @indent__base);
			.lib-css(font-family, @font-family__base);
			.lib-css(font-weight, @font-weight__semibold);
			.lib-css(box-sizing, border-box, 1);
			.lib-css(border, 1px solid @color-gray90);
			.lib-css(border-radius, 2px);
			.lib-css(box-shadow, 0px 2px 14px rgba(0, 0, 0, 0.25), 1);
			.lib-css(transition, box-shadow 0.2s ease, 1);

			&.summary-rewards,
			&.insider-payment-method {
				.lib-css(margin, 0);
				.lib-css(border, 2px solid @color-blue);
				.lib-css(box-shadow, 0 0 0, 1);
				.lib-css(border-radius, 0, 1);

				&:hover {
					.lib-css(box-shadow, 0 0 0, 1);
				}

				.message {
					.lib-css(color, @color-blue);
				}
			}

			&.summary-rewards {
				cursor: pointer;
				background-color: white;
				border-width: 1px;
				.lib-css(border-radius, 2px, 1);
				.lib-css(margin-top, @indent__base);

				.message {
					.lib-css(font-family, @font-family__base);
					.lib-css(color, @color-blue);
					.lib-css(font-weight, @font-weight__bold);
					.lib-line-height(16);

					&:after {
						content: '!';
					}
				}
			}

			&:hover {
				.lib-css(box-shadow, 0px 2px 14px rgba(0, 0, 0, 0.35), 1);
			}

			.wrapper {
				.lib-css(padding, @indent__s @indent__m);
				.lib-css(box-sizing, border-box, 1);
				.lib-vendor-prefix-display();
				.lib-vendor-box-align(center);

				.icon-col {
					.lib-css(padding-right, @indent__s);
					text-align: center;
					max-width: 60px;
				}
			}

			.insider-rewards-icon {
				.lib-font-size(32);
				.lib-line-height(32);
				.lib-icon-font(
					@_icon-font-content: @tt-icon-insider-rewards,
					@_icon-font-color: @color-red2
				);
			}

			p {
				.lib-font-size(16);
				.lib-line-height(20);
				.lib-css(margin, 0);
			}

			a {
				text-decoration: underline;

				&:hover {
					.lib-css(color, @color-blue5);
				}
			}

			.title {
				.lib-css(color, @link__color);
				.lib-font-size(18);
				.lib-css(font-weight, @font-weight__bold);

				a {
					.lib-css(font-weight, @font-weight__semibold);
					.lib-font-size(16);
				}
			}

			.message {
				.lib-css(color, @color-gray20);
			}

			.feature {
				.lib-css(color, @color-green3);
			}
		}

		.sidebar-totals .table.table-totals {
			.redeemed-rewards {
				td {
					text-align: left;
					.lib-css(color, @color-green3);
					.lib-css(font-family, @font-family__base);
					.lib-css(font-weight, @font-weight__bold);
					.lib-font-size(16);
				}
			}

			.rewardpoints {
				th, td {
					.lib-css(color, @color-green3);
					.lib-css(font-family, @font-family__base);
					.lib-css(font-weight, @font-weight__bold);
					.lib-font-size(16);

					a {
						.lib-css(font-weight, @font-weight__regular);

						&.action {
							&.delete {
								cursor: pointer;
							}
						}
					}
				}
			}
		}

		//
		//  Shipping Address
		//  ---------------------------------------------

		.form-login,
		.form-shipping-address {
			// .lib-css(margin-top, @checkout-shipping-address__margin-top);
			// .lib-css(margin-bottom, @indent__base);

			input,
			select {
				height: @checkout-form-input__height;
				.lib-line-height(@checkout-form-input__height);
			}

			.fieldset {
				h3 {
					font-family: @font-family__base;
					font-weight: 700;
					.lib-font-size(20);
					.lib-line-height(22);
					letter-spacing: 0.2px;
					color: @color-blue;
					margin: 60px 0 20px;

					&:before {
						position: absolute;
						left: 0;
						margin-top: -20px;
						content: '';
						width: 100%;
						height: 1px;
						background-color: @color-gray4;
					}

					.checkout-index-index & {
						.lib-css(margin, @indent__m 0 @indent__m);

						&:before {
							display: none;
						}
					}
				}
			}
		}

		.form-login {
			.note {
				margin-top: @indent__s;
				display: block;
				.lib-font-size(13);
				.lib-line-height(16);

				&:before {
					display: none;
				}
			}

			.field {
				margin-bottom: 0;
			}

			.password-fields {
				margin-top: @indent__base;

				.forgot-password {
					.lib-css(font-family, @font-family__base);
					.lib-clearfix();
					.lib-css(margin, @indent__m 0 @indent__base);
				}

				#msp-recaptcha-checkout-inline-login-wrapper {
					margin-bottom: 20px;
				}
			}

			.button-block {
				margin-bottom: 12px;
			}

			.or {
				text-transform: uppercase;
				font-style: italic;
				color: @color-gray40;
				text-align: center;
				margin-bottom: 12px;
				.lib-clearfix();
			}

			.actions-toolbar {
				margin: 0 !important;
				width: 100%;
			}

			.login-tooltip {
				.lib-tooltip(top);

				.tooltip-toggle {
					.lib-icon-font(
						@_icon-font-content: @tt-icon-question,
						@_icon-font-size: 18px,
						@_icon-font-margin: -2px 1px 0,
						@_icon-font-color: @color-blue,
						@_icon-font-vertical-align: top,
						@_icon-font-position: before,
						@_icon-font-text-hide: true
					);
				}

				.tooltip-content {
					.lib-font-size(14);
				}
			}

			.control {
				&._with-tooltip {
					.field-tooltip { 
						&.toggle {
							display: none;
						}
					}
				}
			}

			.actions-toolbar {
				&#email-input-actions {
					.button-block {
						&.login,
						&.continue {
							width: 100%;
						}
					}
				}
			}
		}

		.shipping-address-items,
		.billing-address-items {
			margin: 0 -@indent__base / 2;
			.lib-vendor-prefix-display();
			.lib-vendor-prefix-flex-wrap();
			.lib-clearfix();
		}

		.shipping-address-item,
		.billing-address-item {
			&:extend(.abs-add-box-sizing all);
			padding: 0 10px;
			display: inline-block;
			float: left;
			font-size: @font-size__base;
			position: relative;
			vertical-align: top;
			word-wrap: break-word;
			.lib-line-height(@checkout-shipping-item__line-height);

			&.billing-address-item {
				padding: 0;
				margin: @indent__m 0 @indent__s;
				float: none;
				display: block;
				.lib-clearfix();
			}

			.address-wrapper {
				.lib-css(border, @checkout-shipping-item__border);
				// .lib-css(margin, @checkout-shipping-item__margin);

				address {
					.lib-css(padding, @indent__base);
					.lib-css(margin, 0);
				}

				.action-select-shipping-item,
				.action-select-billing-item {
					.lib-css(background-color, @checkout-shipping-item-before__border-color);
					.lib-css(color, @color-white);
					.lib-css(border, 0 none);
					.lib-css(border-radius, 0, 1);
					.lib-font-size(17);
					.lib-line-height(46);
					.lib-css(font-family, @font-family__base);
					font-weight: 700;
					text-transform: none;
					display: block;
					width: 100%;
					height: 44px;
					padding: 0 @indent__base;
				}

				.edit-address-link {
					&:extend(.abs-action-button-as-link all);
					display: block;
					margin: 26px 5px 0 0;
				}
			}

			&.selected-item {
				.address-wrapper {
					.lib-css(font-family, @font-family__base);
					.lib-css(border-color, @checkout-shipping-item__active__border-color);
					position: relative;

					&:after {
						.lib-css(background, @checkout-shipping-item-icon__background-color);
						.lib-css(color, @checkout-shipping-item-icon__color);
						.lib-css(content, @tt-icon-check-bold);
						.lib-css(font-family, @icons__font-name);
						.lib-css(height, @checkout-shipping-item-icon__selected__height);
						.lib-css(width, @checkout-shipping-item-icon__selected__width);
						font-size: 18px;
						line-height: 34px;
						position: absolute;
						right: 0;
						text-align: center;
						top: 0;
					}
				}

				.action-select-shipping-item,
				.action-select-billing-item {
					&:extend(.abs-no-display-s all);
					visibility: hidden;
					display: none;
				}
			}
		}

		.field {
			&.addresses {
				&:extend(.abs-add-clearfix all);
				display: block !important;
			}
		}

		.fieldset > .field[name="shippingAddress.custom_attributes.is_business_address"] {
			> label {
				.lib-css(display, none);
			}
		}

		#shipping-step-details {
			.addresses {
				.step-title {
					padding: 10px 0;
				}
			}
		}

		#shipping-new-address-form {
			.field:last-child,
			.field:not([style*="display: none"]):last-child {
				margin-bottom: 0;
			}
		}

		.action-show-popup {
			&:extend(button.button-primary__outline);
			min-width: 152px;
			display: inline-block !important;
			.lib-css(margin, @indent__xs 0 @indent__base);

			> span {
				&:before {
					content: '+';
					padding-right: @indent__xs;
				}
			}
		}
	}

	fieldset {
		&.street {
			[name="street[1]"] {
				.lib-css(margin-top, @indent__base);
			}
		}
	}

	.checkout-container {
		.form-login {
			.field {
				.label-as-guest {
					.lib-font-size(12);
					.lib-line-height(14);
					font-weight: 700;
					padding: 2px 4px;
					.lib-css(border-radius, 3px, 1);
					color: @color-white;
					background: @color-blue;
					text-transform: uppercase;
					margin-bottom: 5px;
					display: inline-block;
				}
			}
		}

		.logged-user {
			.label-as-logged {
				.lib-font-size(12);
				.lib-line-height(14);
				font-weight: 700;
				padding: 2px 4px;
				.lib-css(border-radius, 3px, 1);
				color: @color-white;
				background: @color-green6;
				text-transform: uppercase;
				margin-bottom: 5px;
				display: inline-block;
			}

			.input-as-logged {
				.lib-font-size(16);
				.lib-line-height(40);
				height: 42px;
				background: @color-white;
				border: 1px solid @color-gray80;
				.lib-css(border-radius, 3px, 1);
				border-radius: 3px;
				padding: 0 15px;

				span {
					width: 100%;
					display: block;
					white-space: nowrap;
					overflow-x: auto;

					&::-webkit-scrollbar-track,
					&::-webkit-scrollbar,
					&::-webkit-scrollbar-thumb {
						display: none;
					}
				}
			}

			input {
				border: 0;
				border-radius: 0;
				padding: 0;
				box-shadow: unset !important;
			}
		}
	}

	//
	//  Conversionally Test #12
	//  ---------------------------------------------

	#customer-email-fieldset {
		.field:first-child .note {
			display: none !important;
		}
	}

	.shipping-form-modal {
		.checkout-pickup {
			display: none !important;
		}
	}

	//
	//  Shipping Methods
	//  ---------------------------------------------

	.checkout-shipping-method {
		list-style: none;
		.lib-css(box-sizing, border-box, 1);

		.methods .actions-toolbar,
		.methods .item-title {
			display: none !important;
		}

		.methods {
			.lib-vendor-prefix-display;
			.lib-vendor-prefix-flex-direction(column);
			.lib-css(box-sizing, border-box, 1);
			.lib-css(margin, 0);

			.field {
				background-color: @color-gray97;
				border: 1px solid @color-gray4;
				font-family: @font-family__base;
				font-weight: bold;
				padding: 20px;
				position: relative;
				margin: 0;
				.lib-icon-font(
					@_icon-font-content: @tt-icon-truck,
					@_icon-font-size: 25px,
					@_icon-font-color: @color-gray85,
					@_icon-font-display: block,
					@_icon-font-margin: -18px 0 0,
					@_icon-font-vertical-align: middle,
					@_icon-font-position: before
				);

				&.shippit-express-alt {
					.lib-icon-font(
						@_icon-font-content: @tt-icon-thunder,
						@_icon-font-size: 15px,
						@_icon-font-color: @color-gray85,
						@_icon-font-display: block,
						@_icon-font-margin: 0,
						@_icon-font-vertical-align: middle,
						@_icon-font-position: after
					);

					&:after {
						top: 25%;
						right: 2.5%;
						.lib-css(transform, translateY(-25%), 1);
					}
				}

				&.shippit-priority {
					.lib-icon-font(
						@_icon-font-content: @tt-icon-clock,
						@_icon-font-size: 14px,
						@_icon-font-color: @color-gray85,
						@_icon-font-display: block,
						@_icon-font-margin: 0,
						@_icon-font-vertical-align: middle,
						@_icon-font-position: after
					);

					&:after {
						top: 35%;
						right: 2%;
						.lib-css(transform, translateY(-50%), 1);
					}
				}

				&.shippit-express {
					.lib-icon-font(
						@_icon-font-content: @tt-icon-delivery,
						@_icon-font-size: 18px,
						@_icon-font-color: @color-gray85,
						@_icon-font-display: block,
						@_icon-font-margin: 0,
						@_icon-font-vertical-align: middle,
						@_icon-font-position: before
					);

					&:before {
						top: 30%;
					}

					&:after {
						content: '';
						display: block;
						width: 16px;
						height: 16px;
						background: transparent url('../images/aus_post.svg') no-repeat;
						background-size: cover;
						position: absolute;
						top: 16%;
						right: 2%;
						.lib-css(filter, grayscale(1));
						.lib-css(opacity, 0.3, 1);
					}
				}

				&.shippit-ondemand {
					&:before {
						content: '';
						display: block;
						width: 24px;
						height: 24px;
						background: transparent url('../images/uber.png') no-repeat;
						background-size: cover;
						top: 60%;
						right: 5.5%;
						.lib-css(filter, grayscale(1), 1);
						.lib-css(opacity, 0.3, 1);
					}
				}

				&:before,
				&:after {
					position: absolute;
					top: 50%;
					right: 5%;
				}

				&.active {
					background-color: white;

					&:before {
						.lib-css(color, @color-blue);
					}
				}

				&.shippitcc-shippitcc {
					&:before {
						content: @tt-icon-truck-load;
					}
				}

				label {
					display: block;
					width: 100%;
					margin: 0;
					font-weight: bold;
					.lib-font-size(16);
					padding-right: 40px;

					> .price {
						&:before {
							content: ' - ';
						}
					}
				}
			}

			.amcheckout-method.-selected {
				.field {
					.lib-css(background-color, @color-white);

					&:before {
						z-index: 5;
						.lib-css(color, @color-blue);
					}

					&:after {
						z-index: 10;
						.lib-css(color, @color-red2);
						.lib-css(text-shadow, 0 0 10px rgba(255, 255, 255, 0.75), 1);
					}

					&.shippit-ondemand,
					&.shippit-express {
						&:before {
							.lib-css(color, @color-red2);
							.lib-css(filter, grayscale(0), 1);
							.lib-css(opacity, 1, 1);
						}

						&:after {
							.lib-css(filter, grayscale(0), 1);
							.lib-css(opacity, 1, 1);
						}
					}
				}
			}

			.priority-methods {
				border: 1px solid @color-gray4;
				.lib-css(padding, @indent__base);

				&-title {
					position: relative;
					cursor: pointer;
					.lib-css(font-family, @font-family__base);
					.lib-css(color, @color-blue);
					.lib-css(margin, 0);
					.lib-font-size(18);

					.lib-icon-font(
						@_icon-font-content: @icon-down,
						@_icon-font-display: block,
						@_icon-font-position: after
					);

					&:after {
						position: absolute;
						right: 0;
						.lib-css(transition, all 0.1s ease, 1);
					}
				}

				.field label {
					.lib-font-size(16);
				}

				&._active {
					.priority-methods-title {
						.lib-css(margin-bottom, @indent__m);

						&:after {
							.lib-css(transform, rotate(180deg), 1);
						}
					}
				}
			}

			#arrive-by-estimate {
				display: block;
				font-weight: 700;
				.lib-font-size(14);
				.lib-css(color, @color-green3);
				.lib-css(font-family, @font-family__base);
				.lib-css(transform, translateY(8px), 1);
			}

			#checkout-express-message {
				display: block;
				font-weight: 700;
				.lib-font-size(14);
				.lib-css(color, @color-green3);
				.lib-css(font-family, @font-family__base);
				.lib-css(transform, translateY(8px), 1);
			}
		}

		.shipping-dangerous {
			font-family: @font-family__base;
			background-color:  @color-gray85;
			padding: 13px @indent__m 14px;
			text-align: center;
			color: white;
			margin-bottom: @indent__s;
			.lib-line-height(18);
			.lib-css(box-sizing, border-box, 1);

			.shipping-tooltip {
				.lib-tooltip(left);
				.tooltip-toggle {
					.lib-icon-font(
						@_icon-font-content: @tt-icon-info,
						@_icon-font-size: 18px,
						@_icon-font-margin: 0 3px,
						@_icon-font-vertical-align: top,
						@_icon-font-position: before,
						@_icon-font-text-hide: true
					);

					&:after {
						overflow: visible;
					}
				}

				.tooltip-content {
					.lib-font-size(14);
				}
			}
		}

		.no-quotes-block {
			margin: @indent__base 0;
		}

		.list-store-to-pickup {
			margin-top: 15px;

			> label {
				margin-bottom: 10px;
				display: block;
			}

			select {
				padding: 0 40px 0 15px;
				margin-bottom: 10px;

				&.list-store-select {
					width: 100%;
				}
			}
		}

		.info-store-checkout {
			br {
				display: none;
			}

			p {
				margin-bottom: 10px;
			}
		}
	}

	.shipping-methods-note {
		.lib-css(font-family, @font-family__base);
		.lib-font-size(14);
		.lib-css(padding, 0 0 @indent__m);
	}

	.methods-shipping .methods {
		.actions-toolbar {
			.action {
				&.primary {
					&:extend(.abs-button-l all);
					margin: @indent__base 0 0;
				}
			}
		}
	}

	.table-checkout-shipping-method {
		thead {
			th {
				display: none !important;
			}
		}

		tbody {
			td {
				.lib-css(border-top, @checkout-shipping-method__border);
				.lib-css(padding-bottom, @checkout-shipping-method__padding);
				.lib-css(padding-top, @checkout-shipping-method__padding);

				&:first-child {
					padding-left: 0;
					padding-right: 0;
					width: 20px;
				}

				&.col-price {
					text-align: center;
				}
			}

			tr {
				&:first-child {
					td {
						border-top: none;
					}
				}
			}

			.row-error {
				td {
					border-top: none;
					padding-bottom: @indent__s;
					padding-top: 0;
				}
			}
		}
	}

	// Shipping actions toolbar
	#shipping-method-buttons-container {
		.primary,
		.primary- {
			.button {
				.lib-icon-font(
					@_icon-font-content: @tt-icon-padlock,
					@_icon-font: @icons__font-name,
					@_icon-font-position:before,
					@_icon-font-margin:0 0 5px 0
				);
			}

			.button-mobile {
				display: none;
			}
		}
	}

	.fieldset {
		> .field {
			text-align: left;

			.field-error {
				.lib-font-size(12);
				.lib-css(color, @color-red);
			}
		}
	}

	.help-block {
		.lib-font-size(14);
	}

	.error-message {
		&:extend(.message.error all);
	}

	//
	//  Button overrides
	//  ---------------------------------------------
	.action-save-address {
		.lib-button-secondary();
	}

	.action.edit-address-link,
	.action.secondary.action-hide-popup {
		.lib-button-outline();
	}

	//
	//  Store Pickup
	//  ---------------------------------------------
	.checkout-pickup {
		.lib-clearfix();
		.step-title {
			border: none;
			margin-top: 30px;
			padding: 0;
			letter-spacing: 0px;
			text-transform: none;
		}
		.checkout-pickup-content {
			letter-spacing: 0px;
			text-transform: none;
			border: 0;
			padding: 0;

			.block-title {
				h2 {
					.lib-font-size(18);
					.lib-css(color, @color-blue);
					.lib-css(font-weight, @font-weight__bold);
					.lib-css(margin, 0 0 @indent__xs);
				}
			}

			.selected-store {
				.lib-font-size(14);
			}

			.store-detail {
				max-width: 100%;
				.lib-css(box-sizing, border-box,1 );
				.lib-css(margin, @indent__s 0 0);

				.bordered-box {
					width: 100%;
					.lib-css(box-sizing, border-box, 1);

					&:before {
						content: '\f276';
						font-family: 'TT Icons';
						font-size: inherit;
						color: @color-blue;
						display: inline-block;
						float: left;
						margin-right: 5px;
						font-size: 17px;
					}

					.box-content {
						width: 93%;
					}
				}

				.store-form {
					.store-zipcode {
						&:after {
							.lib-css(opacity, 0, 1);
							.lib-css(transition, all 0.2s ease, 1);
						}

						&:active,
						&:hover {
							&:after {
								.lib-css(opacity, 1, 1);
							}
						}
					}
				}
			}

			.title {
				.lib-css(margin-bottom, @indent__s);
				.lib-heading-typography(16, 18, @color-blue);
			}

			h4 {
				margin: 0;
				line-height: 2rem;
				.lib-font-size(16);
			}

			.store-phone {
				.lib-css(display, block);
				.lib-css(margin-bottom, @indent__s);
			}

			table {
				width: auto;

				th {
					text-align: left;
				}

				tbody {
					td {
						padding: 3px 0;

						&.row {
							padding-right: 15px;
						}
					}
				}
			}

			.pickup-confirm {
				padding: 23px 0 17px;
				position: relative;

				&:before,
				&:after {
					content: '';
					background: @color-gray4;
					position: absolute;
					top: 0;
					left: -26px;
					width: ~'calc(100% + 52px)';
					height: 1px;
				}

				&:after {
					top: auto;
					bottom: 0;
				}

				.pickup-confirm-title {
					margin-bottom: 18px;

					h4 {
						display: inline-block;
					}

					.tooltip-toggle {
						color: @color-blue;

						&:before {
							margin: -1px 4px 0;
						}
					}
				}
			}
		}

		.selected-store-label {
			.lib-font-size(16);
			.lib-css(margin, @indent__s 0);
		}

		.bordered-box {
			border: 2px solid @color-blue;
			padding: 10px 15px;
			display: flex;
			width: auto;
			display: inline-block;
			position: relative;
			margin-bottom: 15px;
			.checkout-container & {
				padding: 0;
				margin: 0;
				border: 0 none;
			}
			&.ticked:before {
				height: 20px;
				width: 20px;
				font-family: 'TT Icons';
				position: absolute;
				right: 0;
				content: '\e80b';
				background: @color-blue;
				color: @color-white;
				top: 0;
				padding: 5px;
				.lib-font-size(20);
				.lib-line-height(15);
				text-align: center;
			}
			.remove-item {
				position: absolute;
				right: 15px;
				color: #A2A2A2;
				.lib-icon-font(
					@tt-icon-cross,
					@_icon-font-size: 14px,
					@_icon-font-text-hide: true
				);
				&:hover {
					text-decoration: none;
					color: @color-blue;
				}
				.checkout-container & {
					right: 0;
				}
			}
			.box-content {
				display: inline-block;
				.store-name {
					.lib-font-size(18);
					font-weight: bold;
					margin-bottom: 5px;

					p {
						.lib-font-size(16);
						font-weight: 400;
						margin-bottom: 0;
						text-indent: -@indent__m;
					}
				}

				.store-info {
					letter-spacing: 0px;
					position: relative;

					&:before {
						content: '\ea0c';
						font-family: 'TT Icons';
						font-size: inherit;
						display: inline-block;
						margin-right: 5px;
						font-size: 15px;
						padding: 0px;
						position: absolute;
					}

					> span {
						padding-left: 25px;
					}

					.left {
						font-weight: bold;
					}
					.right {
						.lib-font-size(14);
					}
				}
			}
			&.btn {
				text-align: center;
				.title {
					margin: 0 25px;
				}
			}
		}

		.store-location-edit {
			.lib-css(color, @link__color);
			.lib-css(cursor, pointer);

			&:hover {
				text-decoration: underline;
			}
		}

		.store-detail-content {
			position: relative;
			display: contents;

			p {
				margin-bottom: 1rem;
			}

			.store-direction-url,
			.store-location-edit {
				.lib-css(color, @color-blue);
				cursor: pointer;

				&:hover {
					text-decoration: underline;
				}
			}

			.store-phone {
				display: block;
				.lib-css(margin-bottom, @indent__s);
			}

			.available {
				color: @color-green;
			}

			.not-available {
				color: @color-red;
			}

			.low-stock {
				color: @color-orange;
			}

		}
		
		.postcode-or-suburb {
			.store-form {
				margin-bottom: 0;
			}
		}

		.store-form {
			max-width: none;
			position: relative;
			margin-bottom: @indent__s;
			#change-store-dropdown {
				z-index: 999;
				position: absolute;
				background: @color-white;
				width: 100%;
				max-height: 360px;
				top: 62px;
				overflow-y: scroll;
				border-bottom: 1px solid #ccc;
				.store-options {
					border-left: 1px solid #ccc;
					border-right: 1px solid #ccc; //padding: 0 20px;
					top: -1px;
					.store-list {
						.item-suburb {
							border: none;
						}
						.item-store {
							display: block;
							width: 100%;
							padding: 0;
							margin: 0;
							cursor: pointer;
							.item-store-content {
								padding: 5px 15px;
							}
							:hover {
								background-color: #eee;
							}

							.name {
								font-weight: 700;
								.lib-font-size(16);
							}
						}
					}
				}
			}

			.field[name="shippingAddress.extension_attributes.storelocator_id"] {
				display: none !important;
			}
		}

		.stock-status {
			.lib-css(color, @color-red2);

			strong {
				margin-top: 5px;
				display: block;
				width: 100%;
			}
		}
	}
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum ='max') and (@break = @screen__m) {
	.opc-wrapper {
		.form-login {
			.lib-css(margin, 0);
			.lib-css(padding, 0);

			.note {
				display: none !important;
			}
		}

		.shipping-address-items,
		.billing-address-items {
			.lib-vendor-prefix-flex-direction(row);
		}

		.shipping-address-item,
		.billing-address-item {
			.lib-css(margin, @checkout-shipping-item-mobile__margin);
			.lib-css(padding, @checkout-shipping-item-mobile__padding);
			width: 100%;
		}

		.form-shipping-address {
			.lib-css(margin, 0);
		}

		#checkout-step-shipping {
			padding-top: @indent__m + 10px;
			padding-bottom: @indent__m;
		}

		#shipping-step-details {
			.checkout-billing-address {
				padding-top: @indent__base;
				padding-bottom: 0;
			}
		}

		.action-select-shipping-item,
		.action-select-billing-item {
			float: none;
			margin-top: @indent__s;
			width: 100%;
		}

		.action-show-popup {
			width: 100%;
			margin: 5px 0 @indent__base;
		}
	}

	#shipping-method-buttons-container {
		.primary,
		.primary- {
			.button-mobile {
				display: block;
			}
		}
	}
}

//
//  Tablet
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__m) {
	.checkout-shipping-method .methods {
		.actions-toolbar {
			> .primary {
				float: right;
			}

			.action {
				&.primary {
					margin: 0;
				}
			}
		}
	}

	.opc-wrapper {
		.form-login,
		.form-shipping-address {
			.lib-css(max-width, @checkout-shipping-address__max-width);
		}

		.shipping-address-item,
		.billing-address-item {
			.lib-css(width, @checkout-shipping-item-tablet__width);
			.lib-css(margin, @checkout-shipping-item__margin);
		}

		.store-pickup {
			.address-list-wrapper {
				min-height: 400px;
			}

			.field.addresses {
				width: 48%;

				.shipping-address-item {
					width: 100%;
				}
			}
		}
	}

	.table-checkout-shipping-method {
		width: auto;
	}

	.error-message {
		.lib-css(max-width, @checkout-shipping-address__max-width);
		.lib-css(box-sizing, border-box, 1);
	}
	//
	//  Shipping Methods
	//  ---------------------------------------------

	.checkout-shipping-method {
		width: 49%;
		float: left;
		display: inline-block;

		.step-title {
			white-space: nowrap;
		}
	}
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__l) {
	.opc-wrapper {
		.shipping-address-item,
		.billing-address-item {
			.lib-css(width, @checkout-shipping-item__width);
			.lib-css(margin, @checkout-shipping-item__margin);

			&:nth-child(3n + 1) {
				&:before {
					display: none;
				}
			}

			&.selected-item {
				&:before {
					display: none;
				}

				+ .shipping-address-item {
					&:before {
						display: none;
					}
				}

				+ .billing-address-item {
					&:before {
						display: none;
					}
				}
			}
		}
	}
}
