<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Controller\Verify;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Validator\EmailAddress;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\OtpLogin\Model\OtpService;

class Index extends Action implements HttpGetActionInterface
{
    /**
     * @var PageFactory
     */
    private $resultPageFactory;

    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var OtpService
     */
    private $otpService;

    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var EmailAddress
     */
    private $emailValidator;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param RedirectFactory $resultRedirectFactory
     * @param OtpService $otpService
     * @param Session $customerSession
     * @param CustomerRepositoryInterface $customerRepository
     * @param EmailAddress $emailValidator
     * @param ManagerInterface $messageManager
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        RedirectFactory $resultRedirectFactory,
        OtpService $otpService,
        Session $customerSession,
        CustomerRepositoryInterface $customerRepository,
        EmailAddress $emailValidator,
        ManagerInterface $messageManager
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->otpService = $otpService;
        $this->customerSession = $customerSession;
        $this->customerRepository = $customerRepository;
        $this->emailValidator = $emailValidator;
        $this->messageManager = $messageManager;
    }

    /**
     * Display OTP verification page
     *
     * @return Page|Redirect
     */
    public function execute()
    {
        if ($this->customerSession->isLoggedIn()) {
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account');
        }

        if (!$this->otpService->isEnabled()) {
            $this->messageManager->addErrorMessage(__(['OTP login is not enabled.']));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account/login');
        }

        $email = $this->getRequest()->getParam('email');

        if (empty($email) || !$this->emailValidator->isValid($email)) {
            $this->messageManager->addErrorMessage(__(['Invalid email address.']));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account/login');
        }

        try {
            $this->customerRepository->get($email);
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__(['No account found with this email address.']));
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account/login');
        }

        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('Verify OTP'));
        return $resultPage;
    }
}