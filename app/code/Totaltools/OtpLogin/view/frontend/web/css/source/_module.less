/**
 * Copyright © Totaltools. All rights reserved.
 * OTP Login Module Styles
 */


& when (@media-common =true) {
    .customer-account-login {

        // Login Method Toggle
        .login-method-toggle {
            margin-bottom: 15px;

            .radio {
                margin-right: 8px;
            }

            .label {
                font-weight: 500;
                cursor: pointer;
                display: inline-block;
                margin-bottom: 5px;

                &:hover {
                    color: @color-blue;
                }
            }
        }

        // OTP Method Selection
        .otp-method-selection {
            margin: 15px 0;
            padding: 15px;
            background-color: @color-gray13;
            border-radius: 4px;
            border: 1px solid @color-gray;

            .label {
                font-weight: 500;
                margin-bottom: 10px;
                display: block;
            }

            .control {
                .radio {
                    margin-right: 8px;
                    margin-bottom: 8px;
                }

                .label {
                    display: inline-block;
                    margin-right: 20px;
                    margin-bottom: 0;
                    cursor: pointer;

                    &:hover {
                        color: @color-blue;
                    }
                }
            }
        }

        // Loading States
        .otp-loading {
            text-align: center;
            padding: 20px;
            background-color: fade(@color-gray13, 50%);
            border-radius: 6px;
            margin: 15px 0;

            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid @color-gray;
                border-radius: 50%;
                border-top-color: @color-blue;
                animation: spin 1s ease-in-out infinite;
                margin-bottom: 10px;
            }

            p {
                margin: 0;
                font-weight: 500;
                color: @color-gray20;
                margin: 0;
            }
        }
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }


    // OTP Verification Page
    .otplogin-verify-index {
        .block-otp-verify {
            .block-title {
                padding: 0 0 11px;
                margin: 0 0 16px;
                border-bottom: 1px solid @color-gray90;
                text-transform: uppercase;
                letter-spacing: -.3px;
                color: @color-gray20;
                font-weight: 700;
                .lib-font-size(17);
                line-height: 1.25;
            }

            .otp-info {
                margin-bottom: 28px;
            }

            .form-otp-verify {
                .label {
                    width: 100%;
                    text-align: left;
                    display: inline-block;
                    margin-bottom: 10px;
                    color: @color-gray40;
                    .lib-font-size(16);
                }

                // OTP Input Styling
                .otp-input {
                    font-size: 18px !important;
                    font-weight: 600;
                    text-align: center;
                    letter-spacing: 4px;
                    padding: 12px !important;
        
                    &.mage-error {
                        border-color: @color-red;
                    }
                }
            }

            // Form Actions
            .actions-toolbar {
                .primary {
                    display: inline-block;
                    margin-bottom: 10px;
                    text-align: left;

                    .action.login,
                    .action.submit {
                        width: auto;
                        background: @color-blue;
                        border: 2px solid @color-blue8;
                        color: @color-white;
                        cursor: pointer;
                        display: inline-block;
                        font-weight: 700;
                        margin: 0;
                        padding: 10px 20px;
                        .lib-font-size(14);
                        .lib-line-height(14);
                        text-transform: uppercase;
                        .lib-css(border-radius, 3px, 1);

                        &:hover {
                            background: @color-blue8;
                            border: 2px solid @color-blue5;
                        }
                    }
                }

                .secondary {
                    display: inline-block;
                    padding-top: 10px;
                    margin-left: 25px;

                    .action {
                        color: @color-blue;
                        text-decoration: none;
                    }
                }
            }

            // Timer and Resend Section
            .otp-timer {
                margin: 20px 0;

                .control {
                    #resend-container {
                        font-size: 14px;
                        color: @color-gray20;

                        #countdown {
                            font-weight: 600;
                            color: @color-blue;
                        }
                    }

                    .resend-btn {
                        font-size: 14px;
                        padding: 8px 16px;
                        background-color: transparent;
                        border: 2px solid @color-blue;
                        color: @color-blue;

                        &:hover {
                            background-color: @color-blue;
                            color: @color-white;
                        }

                        &:disabled {
                            opacity: 0.6;
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }
    }
}

//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break = @screen__m) {
    .otplogin-verify-index {
        .block-otp-verify {
            .block-title {
                display: none;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) {
    .otplogin-verify-index {
        .block-otp-verify {
            width: 50%;
        }
    }
}