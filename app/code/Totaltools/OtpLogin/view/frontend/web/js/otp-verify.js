/**
 * Copyright © Totaltools. All rights reserved.
 * OTP Verification JavaScript Component
 */
define([
    'jquery',
    'mage/url',
    'mage/translate',
    'mage/validation',
    'jquery/ui'
], function ($, urlBuilder, $t) {
    'use strict';

    return function (config, element) {
        var $form = $(element);
        var $otpInput = $form.find('#otp_code');
        var $verifyBtn = $form.find('#verify-otp-btn');
        var $resendBtn = $form.find('#resend-otp-btn');
        var $countdown = $('#countdown');
        var $timerText = $('#timer-text');
        var $timerSuffix = $('#timer-suffix');
        var $loadingDiv = $('#verify-loading');
        var $messagesDiv = $('#verify-messages');
        
        var verifyInProgress = false;
        var resendInProgress = false;
        var countdownTimer = null;
        var resendCountdown = 60; // seconds

        // Initialize
        init();

        function init() {
            bindEvents();
            startResendCountdown();
            focusOtpInput();
            formatOtpInput();
        }

        function bindEvents() {
            // Form submission
            $form.on('submit', function(e) {
                e.preventDefault();
                if (!verifyInProgress && validateOtpInput()) {
                    verifyOtp();
                }
            });

            // OTP input formatting and validation
            $otpInput.on('input', function() {
                formatOtpInput();
                clearMessages();
            });

            // Auto-submit when 6 digits entered
            $otpInput.on('input', function() {
                var value = $(this).val();
                if (value.length === 6 && /^\d{6}$/.test(value)) {
                    setTimeout(function() {
                        if (validateOtpInput()) {
                            verifyOtp();
                        }
                    }, 500); // Small delay for better UX
                }
            });

            // Resend OTP button
            $resendBtn.on('click', function(e) {
                e.preventDefault();
                if (!resendInProgress) {
                    resendOtp();
                }
            });

            // Prevent non-numeric input
            $otpInput.on('keypress', function(e) {
                var char = String.fromCharCode(e.which);
                if (!/[0-9]/.test(char)) {
                    e.preventDefault();
                }
            });

            // Handle paste events
            $otpInput.on('paste', function(e) {
                setTimeout(function() {
                    formatOtpInput();
                }, 10);
            });
        }

        function formatOtpInput() {
            var value = $otpInput.val().replace(/\D/g, ''); // Remove non-digits
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            $otpInput.val(value);
        }

        function focusOtpInput() {
            setTimeout(function() {
                $otpInput.focus();
            }, 100);
        }

        function validateOtpInput() {
            var otp = $otpInput.val().trim();
            
            if (!otp) {
                showError($t('Please enter the OTP code.'));
                $otpInput.focus();
                return false;
            }
            
            if (otp.length !== 6) {
                showError($t('OTP must be 6 digits long.'));
                $otpInput.focus();
                return false;
            }
            
            if (!/^\d{6}$/.test(otp)) {
                showError($t('OTP must contain only numbers.'));
                $otpInput.focus();
                return false;
            }
            
            return true;
        }

        function verifyOtp() {
            var otp = $otpInput.val().trim();
            verifyInProgress = true;
            showLoading();
            clearMessages();

            var recaptchaToken = null;
            if (window.grecaptcha && typeof window.grecaptcha.getResponse === 'function') {
                recaptchaToken = window.grecaptcha.getResponse();
            }

          
            var requestData = {
                otp: otp,
                email: config.email,
                channel: config.channel,
                form_key: $form.find('input[name="form_key"]').val()
            };

            if (recaptchaToken) {
                requestData['g-recaptcha-response'] = recaptchaToken;
                console.log('- reCAPTCHA token added to request');
            } else {
                console.log('- No reCAPTCHA token to add');
            }

            $.ajax({
                url: config.verifyUrl,
                type: 'POST',
                dataType: 'json',
                data: requestData,
                success: function(response) {
                    if (response.success) {
                        showSuccess($t('OTP verified successfully! Redirecting...'));
                        setTimeout(function() {
                            window.location.href = response.redirect_url || config.loginUrl;
                        }, 1000);
                    } else {
                        showError(response.message || $t('Invalid OTP. Please try again.'));
                        $otpInput.val('').focus();
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = $t('An error occurred while verifying OTP. Please try again.');
                    console.log(error);
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    showError(errorMessage);
                    $otpInput.focus();
                },
                complete: function() {
                    verifyInProgress = false;
                    hideLoading();
                }
            });
        }

        function resendOtp() {
            resendInProgress = true;
            $resendBtn.prop('disabled', true);
            clearMessages();
            
            $.ajax({
                url: config.resendUrl,
                type: 'POST',
                dataType: 'json',
                data: {
                    email: config.email,
                    channel: config.channel,
                    form_key: $form.find('input[name="form_key"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess($t('OTP has been resent successfully.'));
                        $otpInput.val('').focus();
                        startResendCountdown();
                    } else {
                        showError(response.message || $t('Failed to resend OTP. Please try again.'));
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = $t('An error occurred while resending OTP. Please try again.');
                    
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    showError(errorMessage);
                },
                complete: function() {
                    resendInProgress = false;
                }
            });
        }

        function startResendCountdown() {
            resendCountdown = 60;
            $resendBtn.hide();
            $timerText.show();
            $countdown.show();
            $timerSuffix.show();
            
            updateCountdownDisplay();
            
            countdownTimer = setInterval(function() {
                resendCountdown--;
                updateCountdownDisplay();
                
                if (resendCountdown <= 0) {
                    clearInterval(countdownTimer);
                    showResendButton();
                }
            }, 1000);
        }

        function updateCountdownDisplay() {
            $countdown.text(resendCountdown);
        }

        function showResendButton() {
            $timerText.hide();
            $countdown.hide();
            $timerSuffix.hide();
            $resendBtn.show().prop('disabled', false);
        }

        function showLoading() {
            $loadingDiv.show();
            $verifyBtn.prop('disabled', true);
            $otpInput.prop('disabled', true);
        }

        function hideLoading() {
            $loadingDiv.hide();
            $verifyBtn.prop('disabled', false);
            $otpInput.prop('disabled', false);
        }

        function showError(message) {
            $messagesDiv.html(
                '<div class="message-error error message">' +
                '<div>' + message + '</div>' +
                '</div>'
            );
        }

        function showSuccess(message) {
            $messagesDiv.html(
                '<div class="message-success success message">' +
                '<div>' + message + '</div>' +
                '</div>'
            );
        }

        function clearMessages() {
            $messagesDiv.empty();
        }

        // Cleanup on page unload
        $(window).on('beforeunload', function() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
        });
    };
});