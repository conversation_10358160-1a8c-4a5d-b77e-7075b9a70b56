<?xml version="1.0"?>
<!--
/**
 * Copyright © Totaltools. All rights reserved.
 * OTP Verification Page Layout
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Totaltools_OtpLogin::css/otp-login.css"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template"
                   name="otp.verify.form"
                   template="Totaltools_OtpLogin::verify.phtml"
                   cacheable="false">
                <container name="form.additional.info" as="form_additional_info">
                    <block class="Magento\ReCaptchaUi\Block\ReCaptcha"
                           name="recaptcha"
                           after="-"
                           template="Magento_ReCaptchaFrontendUi::recaptcha.phtml"
                           ifconfig="recaptcha_frontend/type_for/otp_verification">
                        <arguments>
                            <argument name="recaptcha_for" xsi:type="string">otp_verification</argument>
                            <argument name="jsLayout" xsi:type="array">
                                <item name="components" xsi:type="array">
                                    <item name="recaptcha" xsi:type="array">
                                        <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                    </item>
                                </item>
                            </argument>
                        </arguments>
                    </block>
                </container>
            </block>
        </referenceContainer>
    </body>
</page>