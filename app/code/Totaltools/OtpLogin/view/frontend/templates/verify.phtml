<?php
/**
 * Copyright © Totaltools. All rights reserved.
 * OTP Verification Template
 *
 * @var $block \Magento\Framework\View\Element\Template
 * @var $escaper \Magento\Framework\Escaper
 */

$email = $this->getRequest()->getParam('email') ?: $block->getData('email');
$channel = $this->getRequest()->getParam('channel') ?: 'email';
$maskedContact = $this->helper('Totaltools\OtpLogin\Helper\Data')->getMaskedContact($email, $channel);
?>

<div class="block block-otp-verify">
    <div class="block-title">
        <strong id="block-otp-verify-heading" role="heading" aria-level="1">
            <?= $escaper->escapeHtml(__('Verify OTP')) ?>
        </strong>
    </div>
    <div id="verify-messages" class="messages"></div>
    <div class="block-content" aria-labelledby="block-otp-verify-heading">
        <div class="otp-info">
            <p class="otp-sent-message">
                <?php if ($channel === 'email'): ?>
                    <?= $escaper->escapeHtml(__('We have sent a 6-digit verification code to %1', $maskedContact)) ?>
                <?php else: ?>
                    <?= $escaper->escapeHtml(__('We have sent a 6-digit verification code to your mobile number ending with %1', $maskedContact)) ?>
                <?php endif; ?>
            </p>
        </div>

        <form class="form form-otp-verify" 
              id="otp-verify-form" 
              data-mage-init='{"validation":{}}'>
            
            <input name="form_key" type="hidden" value="<?= $escaper->escapeHtmlAttr($block->getFormKey()) ?>" />
            <input name="email" type="hidden" value="<?= $escaper->escapeHtmlAttr($email) ?>" />
            <input name="channel" type="hidden" value="<?= $escaper->escapeHtmlAttr($channel) ?>" />
            
            <fieldset class="fieldset verify" data-hasrequiredclass="<?= $escaper->escapeHtmlAttr(__('* Required Fields')) ?>">
                
                <!-- OTP Input -->
                <div class="field otp required">
                    <label class="label" for="otp_code">
                        <span><?= $escaper->escapeHtml(__('Enter 6-digit OTP')) ?></span>
                    </label>
                    <div class="control">
                        <input name="otp" 
                               id="otp_code" 
                               type="text" 
                               class="input-text otp-input" 
                               title="<?= $escaper->escapeHtmlAttr(__('OTP Code')) ?>" 
                               maxlength="6"
                               pattern="[0-9]{6}"
                               placeholder="000000"
                               autocomplete="one-time-code"
                               data-validate="{required:true, 'validate-digits':true, 'validate-length':6}" 
                               autofocus />
                    </div>
                </div>

                <!-- Timer and Resend -->
                <div class="field otp-timer">
                    <div class="control">
                        <div id="resend-container">
                            <span id="timer-text"><?= $escaper->escapeHtml(__('Resend OTP in ')) ?></span>
                            <span id="countdown">60</span>
                            <span id="timer-suffix"><?= $escaper->escapeHtml(__(' seconds')) ?></span>
                            
                            <button type="button" 
                                    id="resend-otp-btn" 
                                    class="action secondary resend-btn" 
                                    style="display: none;">
                                <span><?= $escaper->escapeHtml(__('Resend OTP')) ?></span>
                            </button>
                        </div>
                    </div>
                </div>

                <?= $block->getChildHtml('form_additional_info') ?>

                <!-- Actions -->
                <div class="actions-toolbar">
                    <div class="primary">
                        <button type="submit"
                                class="action submit primary"
                                id="verify-otp-btn">
                            <span><?= $escaper->escapeHtml(__('Verify & Login')) ?></span>
                        </button>
                    </div>
                    <div class="secondary">
                        <a class="action back" href="<?= $escaper->escapeUrl($this->getUrl('customer/account/login')) ?>">
                            <span><?= $escaper->escapeHtml(__('Back to Login')) ?></span>
                        </a>
                    </div>
                </div>
            </fieldset>
        </form>

        <!-- Loading Indicator -->
        <div id="verify-loading" class="otp-loading" style="display: none;">
            <div class="loading-spinner"></div>
            <p><?= $escaper->escapeHtml(__('Verifying OTP...')) ?></p>
        </div>

       
    </div>
</div>

<script type="text/x-magento-init">
{
    "#otp-verify-form": {
        "Totaltools_OtpLogin/js/otp-verify": {
            "verifyUrl": "<?= $escaper->escapeUrl($this->getUrl('otplogin/verify/verifypost')) ?>",
            "resendUrl": "<?= $escaper->escapeUrl($this->getUrl('otplogin/index/sendotp')) ?>",
            "loginUrl": "<?= $escaper->escapeUrl($this->getUrl('customer/account')) ?>",
            "email": "<?= $escaper->escapeJs($email) ?>",
            "channel": "<?= $escaper->escapeJs($channel) ?>"
        }
    }
}</script>