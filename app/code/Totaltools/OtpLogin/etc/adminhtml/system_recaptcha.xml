<?xml version="1.0"?>
<!--
/**
 * Copyright © Totaltools. All rights reserved.
 * reCAPTCHA Configuration for OTP Login
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="recaptcha_frontend">
            <group id="type_for">
                <field id="otp_verification" translate="label" type="select" sortOrder="200" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Enable for OTP Verification</label>
                    <source_model>Magento\ReCaptchaAdminUi\Model\OptionSource\Type</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
