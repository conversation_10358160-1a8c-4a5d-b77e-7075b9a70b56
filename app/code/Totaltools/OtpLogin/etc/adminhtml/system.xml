<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="100">
            <label>TotalTools</label>
        </tab>
        <section id="totaltools_otplogin" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>OTP Login Settings</label>
            <tab>totaltools</tab>
            <resource>Totaltools_OtpLogin::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable OTP Login</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>totaltools_otplogin/general/enabled</config_path>
                </field>
                <field id="otp_expiration" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>OTP Expiration Time (minutes)</label>
                    <config_path>totaltools_otplogin/general/otp_expiration</config_path>
                    <validate>validate-digits validate-digits-range digits-range-1-60</validate>
                    <comment>Time in minutes before OTP expires (1-60 minutes)</comment>
                </field>
                <field id="max_attempts" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Maximum OTP Attempts</label>
                    <config_path>totaltools_otplogin/general/max_attempts</config_path>
                    <validate>validate-digits validate-digits-range digits-range-1-10</validate>
                    <comment>Maximum number of OTP verification attempts allowed</comment>
                </field>
                <field id="rate_limit" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Rate Limit (requests per minute)</label>
                    <config_path>totaltools_otplogin/general/rate_limit</config_path>
                    <validate>validate-digits validate-digits-range digits-range-1-10</validate>
                    <comment>Maximum OTP requests per minute per email</comment>
                </field>
            </group>
            <group id="email" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Email OTP</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>totaltools_otplogin/email/enabled</config_path>
                </field>
                <field id="template" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <config_path>totaltools_otplogin/email/template</config_path>
                </field>
                <field id="sender" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <config_path>totaltools_otplogin/email/sender</config_path>
                </field>
            </group>
            <group id="sms" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>SMS Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable SMS OTP</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>totaltools_otplogin/sms/enabled</config_path>
                </field>
            </group>
        </section>
         <section id="recaptcha_frontend">
            <group id="type_for">
                <field id="otp_verification" translate="label" type="select" sortOrder="200" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Enable for OTP Verification</label>
                    <source_model>Magento\ReCaptchaAdminUi\Model\OptionSource\Type</source_model>
                </field>
            </group>
        </section>
    </system>
</config>