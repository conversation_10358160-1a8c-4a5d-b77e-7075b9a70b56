<?xml version="1.0"?>
<!--
/**
 * Copyright © Totaltools. All rights reserved.
 * Dependency Injection Configuration for OTP Login
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- reCAPTCHA Configuration Providers -->
    <type name="Magento\ReCaptchaUi\Model\ValidationConfigResolver">
        <arguments>
            <argument name="validationConfigProviders" xsi:type="array">
                <item name="otp_verification" xsi:type="object">Totaltools\OtpLogin\Model\ReCaptcha\ValidationConfigProvider</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\ReCaptchaUi\Model\UiConfigResolver">
        <arguments>
            <argument name="uiConfigProviders" xsi:type="array">
                <item name="otp_verification" xsi:type="object">Totaltools\OtpLogin\Model\ReCaptcha\UiConfigProvider</item>
            </argument>
        </arguments>
    </type>

    <!-- reCAPTCHA Type Resolver -->
    <type name="Magento\ReCaptchaUi\Model\CaptchaTypeResolver">
        <arguments>
            <argument name="captchaTypePerArea" xsi:type="array">
                <item name="frontend" xsi:type="array">
                    <item name="otp_verification" xsi:type="string">recaptcha_frontend/type_for/otp_verification</item>
                </item>
            </argument>
        </arguments>
    </type>

    <!-- Console Commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="totaltools_otp_debug_recaptcha" xsi:type="object">Totaltools\OtpLogin\Console\Command\DebugRecaptcha</item>
            </argument>
        </arguments>
    </type>

</config>
