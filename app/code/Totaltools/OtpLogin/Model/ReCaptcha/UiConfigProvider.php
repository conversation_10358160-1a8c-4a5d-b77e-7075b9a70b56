<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Model\ReCaptcha;

use <PERSON>gento\ReCaptchaUi\Model\UiConfigProviderInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Provide UI configuration for OTP verification form reCAPTCHA
 */
class UiConfigProvider implements UiConfigProviderInterface
{
    private const CAPTCHA_PUBLIC_KEY_V3 = 'recaptcha_frontend/type_recaptcha_v3/public_key';
    private const CAPTCHA_PUBLIC_KEY_V2_CHECKBOX = 'recaptcha_frontend/type_recaptcha_v2_checkbox/public_key';
    private const CAPTCHA_PUBLIC_KEY_V2_INVISIBLE = 'recaptcha_frontend/type_recaptcha_v2_invisible/public_key';
    private const CAPTCHA_TYPE = 'recaptcha_frontend/type_for/otp_verification';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Get UI config for OTP verification reCAPTCHA
     *
     * @return array
     */
    public function get(): array
    {
        $captchaType = $this->scopeConfig->getValue(
            self::CAPTCHA_TYPE,
            ScopeInterface::SCOPE_WEBSITE
        );

        if (!$captchaType) {
            return [];
        }

        $publicKey = $this->getPublicKeyByType($captchaType);

        if (!$publicKey) {
            return [];
        }

        return [
            'rendering' => [
                'sitekey' => $publicKey,
                'badge' => 'bottomright',
                'size' => 'invisible',
                'theme' => 'light',
                'hl' => 'en'
            ],
            'invisible' => true
        ];
    }

    /**
     * Get public key by reCAPTCHA type
     *
     * @param string $type
     * @return string|null
     */
    private function getPublicKeyByType(string $type): ?string
    {
        $configPath = null;

        switch ($type) {
            case 'recaptcha_v3':
                $configPath = self::CAPTCHA_PUBLIC_KEY_V3;
                break;
            case 'recaptcha_v2_checkbox':
                $configPath = self::CAPTCHA_PUBLIC_KEY_V2_CHECKBOX;
                break;
            case 'recaptcha_v2_invisible':
                $configPath = self::CAPTCHA_PUBLIC_KEY_V2_INVISIBLE;
                break;
        }

        if (!$configPath) {
            return null;
        }

        return $this->scopeConfig->getValue(
            $configPath,
            ScopeInterface::SCOPE_WEBSITE
        );
    }
}
