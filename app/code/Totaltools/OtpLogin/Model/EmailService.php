<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Model;

use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class EmailService
{
    /**
     * Configuration paths
     */
    private const XML_PATH_EMAIL_ENABLED = 'totaltools_otplogin/email/enabled';
    private const XML_PATH_EMAIL_TEMPLATE = 'totaltools_otplogin/email/template';
    private const XML_PATH_EMAIL_SENDER = 'totaltools_otplogin/email/sender';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var StateInterface
     */
    private $inlineTranslation;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param StateInterface $inlineTranslation
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }

    /**
     * Check if email OTP is enabled
     *
     * @return bool
     */
    public function isEmailEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Send OTP via email
     *
     * @param string $email
     * @param string $otp
     * @param string $customerName
     * @return bool
     * @throws LocalizedException
     */
    public function sendOtpEmail(string $email, string $otp, string $customerName = ''): bool
    {
        if (!$this->isEmailEnabled()) {
            throw new LocalizedException(__('Email OTP is not enabled.'));
        }

        try {
            $this->inlineTranslation->suspend();

            $templateId = $this->getEmailTemplate();
            $sender = $this->getEmailSender();
            $storeId = $this->storeManager->getStore()->getId();

            $templateVars = [
                'otp' => $otp,
                'customer_name' => $customerName ?: 'Customer',
                'store_name' => $this->storeManager->getStore()->getName(),
                'expiry_minutes' => $this->getExpirationTime()
            ];

            $transport = $this->transportBuilder
                ->setTemplateIdentifier($templateId)
                ->setTemplateOptions([
                    'area' => Area::AREA_FRONTEND,
                    'store' => $storeId
                ])
                ->setTemplateVars($templateVars)
                ->setFromByScope($sender, $storeId)
                ->addTo($email, $customerName)
                ->getTransport();

            $transport->sendMessage();

            $this->inlineTranslation->resume();

            $this->logger->info("OTP email sent successfully to: {$email}");
            return true;

        } catch (MailException $e) {
            $this->inlineTranslation->resume();
            $this->logger->error("Failed to send OTP email to {$email}: " . $e->getMessage());
            throw new LocalizedException(__('Failed to send OTP email. Please try again.'));
        } catch (\Exception $e) {
            $this->inlineTranslation->resume();
            $this->logger->error("Error sending OTP email: " . $e->getMessage());
            throw new LocalizedException(__('Unable to send OTP email. Please try again.'));
        }
    }

    /**
     * Get email template
     *
     * @return string
     */
    private function getEmailTemplate(): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE,
            ScopeInterface::SCOPE_STORE
        ) ?: 'totaltools_otplogin_email_template';
    }

    /**
     * Get email sender
     *
     * @return string
     */
    private function getEmailSender(): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_SENDER,
            ScopeInterface::SCOPE_STORE
        ) ?: 'general';
    }

    public function getExpirationTime(): int
    {
        return (int)$this->scopeConfig->getValue(
            'totaltools_otplogin/general/expiry_minutes',
            ScopeInterface::SCOPE_STORE
        ) ?: 5; 
    }
}