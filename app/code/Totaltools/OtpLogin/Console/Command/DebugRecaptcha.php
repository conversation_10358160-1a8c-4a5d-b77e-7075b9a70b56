<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Console\Command;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Console\Cli;
use Magento\ReCaptchaUi\Model\IsCaptchaEnabledInterface;
use Magento\ReCaptchaUi\Model\UiConfigResolverInterface;
use Magento\ReCaptchaUi\Model\ValidationConfigResolverInterface;
use Magento\Store\Model\ScopeInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Console command to debug reCAPTCHA configuration for OTP verification
 */
class DebugRecaptcha extends Command
{
    /**
     * @var IsCaptchaEnabledInterface
     */
    private $isCaptchaEnabled;

    /**
     * @var UiConfigResolverInterface
     */
    private $uiConfigResolver;

    /**
     * @var ValidationConfigResolverInterface
     */
    private $validationConfigResolver;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param IsCaptchaEnabledInterface $isCaptchaEnabled
     * @param UiConfigResolverInterface $uiConfigResolver
     * @param ValidationConfigResolverInterface $validationConfigResolver
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        IsCaptchaEnabledInterface $isCaptchaEnabled,
        UiConfigResolverInterface $uiConfigResolver,
        ValidationConfigResolverInterface $validationConfigResolver,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->isCaptchaEnabled = $isCaptchaEnabled;
        $this->uiConfigResolver = $uiConfigResolver;
        $this->validationConfigResolver = $validationConfigResolver;
        $this->scopeConfig = $scopeConfig;
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('totaltools:otp:debug-recaptcha')
            ->setDescription('Debug reCAPTCHA configuration for OTP verification');
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<info>🔍 Debugging reCAPTCHA Configuration for OTP Verification</info>');
        $output->writeln('');

        // Check if reCAPTCHA is enabled
        $isEnabled = $this->isCaptchaEnabled->isCaptchaEnabledFor('otp_verification');
        $output->writeln('✅ reCAPTCHA Enabled: ' . ($isEnabled ? '<info>YES</info>' : '<error>NO</error>'));

        // Check configured type
        $configuredType = $this->scopeConfig->getValue(
            'recaptcha_frontend/type_for/otp_verification',
            ScopeInterface::SCOPE_WEBSITE
        );
        $output->writeln('🔧 Configured Type: ' . ($configuredType ?: '<error>Not set</error>'));

        // Check all possible keys
        $this->checkKeys($output, 'v3', 'recaptcha_frontend/type_recaptcha_v3');
        $this->checkKeys($output, 'v2_checkbox', 'recaptcha_frontend/type_recaptcha_v2_checkbox');
        $this->checkKeys($output, 'v2_invisible', 'recaptcha_frontend/type_recaptcha_v2_invisible');

        if ($isEnabled) {
            try {
                // Test UI configuration
                $uiConfig = $this->uiConfigResolver->get('otp_verification');
                $output->writeln('');
                $output->writeln('<info>🎨 UI Configuration:</info>');
                if (isset($uiConfig['rendering']['sitekey'])) {
                    $siteKey = $uiConfig['rendering']['sitekey'];
                    $output->writeln('  Site Key: <info>' . substr($siteKey, 0, 20) . '...</info>');
                    
                    // Check if it's a test key
                    if ($siteKey === '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI') {
                        $output->writeln('  <comment>⚠️  Using Google test site key</comment>');
                    }
                } else {
                    $output->writeln('  <error>❌ No site key configured</error>');
                }

                // Test validation configuration
                $validationConfig = $this->validationConfigResolver->get('otp_verification');
                $output->writeln('');
                $output->writeln('<info>🔐 Validation Configuration:</info>');
                $privateKey = $validationConfig->getPrivateKey();
                if ($privateKey) {
                    $output->writeln('  Private Key: <info>' . substr($privateKey, 0, 20) . '...</info>');
                    
                    // Check if it's a test key
                    if ($privateKey === '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe') {
                        $output->writeln('  <comment>⚠️  Using Google test private key</comment>');
                    }
                } else {
                    $output->writeln('  <error>❌ No private key configured</error>');
                }

            } catch (\Exception $e) {
                $output->writeln('<error>❌ Error getting configuration: ' . $e->getMessage() . '</error>');
            }
        }

        $output->writeln('');
        $output->writeln('<comment>💡 Troubleshooting Tips:</comment>');
        $output->writeln('1. Clear cache: php bin/magento cache:clean');
        $output->writeln('2. Check domain matches your reCAPTCHA keys');
        $output->writeln('3. Verify keys are for the correct reCAPTCHA type');
        $output->writeln('4. Check browser console for JavaScript errors');

        return Cli::RETURN_SUCCESS;
    }

    /**
     * Check reCAPTCHA keys for a specific type
     */
    private function checkKeys(OutputInterface $output, string $type, string $configPath): void
    {
        $output->writeln('');
        $output->writeln("<info>🔑 {$type} Keys:</info>");
        
        $siteKey = $this->scopeConfig->getValue($configPath . '/public_key', ScopeInterface::SCOPE_WEBSITE);
        $privateKey = $this->scopeConfig->getValue($configPath . '/private_key', ScopeInterface::SCOPE_WEBSITE);
        
        $output->writeln('  Site Key: ' . ($siteKey ? '<info>' . substr($siteKey, 0, 20) . '...</info>' : '<error>Not set</error>'));
        $output->writeln('  Private Key: ' . ($privateKey ? '<info>' . substr($privateKey, 0, 20) . '...</info>' : '<error>Not set</error>'));
    }
}
