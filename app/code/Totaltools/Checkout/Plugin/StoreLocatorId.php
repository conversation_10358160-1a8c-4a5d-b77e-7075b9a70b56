<?php

namespace Totaltools\Checkout\Plugin;

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

class StoreLocatorId
{
    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    protected $helper;

    /**
     * @param \Totaltools\Checkout\Helper\Data $helper
     */
    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shippingAdditional']['children']['store-locator']['children']['store-fields']['children'] = [
            'storelocator_postcode' => [
                'component' => 'Magento_Ui/js/form/element/abstract',
                'config' => [
                    'customScope' => 'storeLocator',
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/input',
                    'id' => 'storelocator-postcode'
                ],
                'provider' => 'checkoutProvider',
                'dataScope' => 'storeLocator.storelocator_postcode',
                'label' => __('Enter Suburb or Postcode'),
                'description' => '',
                'placeholder' => __('Enter Suburb or Postcode'),
                'id' => 'storelocator-postcode',
                'displayArea' => 'store-fields',
                'visible' => true,
            ],
            'storelocator_id' => [
                'component' => 'Magento_Ui/js/form/element/abstract',
                'config' => [
                    'customScope' => 'shippingAddress',
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/input',
                    'id' => 'storelocator-id'
                ],
                'provider' => 'checkoutProvider',
                'dataScope' => 'shippingAddress.extension_attributes.storelocator_id',
                'description' => '',
                'placeholder' => __('Select your pickup store'),
                'displayArea' => 'store-fields',
                'visible' => true,
                'validation' => ['validate-store-locator' => true],
                'sortOrder' => 10,
                'id' => 'storelocator-id'
            ]
        ];

        return $jsLayout;
    }
}
