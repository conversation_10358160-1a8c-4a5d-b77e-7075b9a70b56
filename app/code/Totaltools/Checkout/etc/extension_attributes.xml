<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Quote\Api\Data\AddressInterface">
        <attribute code="storelocator_id" type="string" />
    </extension_attributes>
    <extension_attributes for="Magento\Checkout\Api\Data\ShippingInformationInterface">
        <attribute code="storelocator_id" type="string" />
        <attribute code="third_party_pickup" type="string" />
        <attribute code="third_party_name" type="string" />
    </extension_attributes>
    <extension_attributes for="Magento\Quote\Api\Data\TotalsItemInterface">
        <attribute code="product_id" type="int" />
        <attribute code="sku" type="string" />
        <attribute code="shipping_label" type="string" />
        <attribute code="shipping_dangerous" type="boolean" />
        <attribute code="overweight" type="boolean" />
        <attribute code="knife_compliance" type="boolean" />
    </extension_attributes>
</config>
