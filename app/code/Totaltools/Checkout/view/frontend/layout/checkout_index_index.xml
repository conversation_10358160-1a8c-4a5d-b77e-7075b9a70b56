<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="checkout" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="authentication" xsi:type="array">
                                    <item name="config" xsi:type="array">
                                        <item name="componentDisabled" xsi:type="boolean">true</item>
                                    </item>
                                </item>
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="shipping-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="shippingAddress" xsi:type="array">
                                                    <item name="sortOrder" xsi:type="string">10</item>
                                                    <item name="children" xsi:type="array">
                                                        <item name="shippingAdditional" xsi:type="array">
                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                            <item name="displayArea" xsi:type="string">shippingAdditional</item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="shipping-note" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/shipping-information/note</item>
                                                                    <item name="sortOrder" xsi:type="number">10</item>
                                                                </item>
                                                                <item name="store-locator" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/store-pickup-address</item>
                                                                    <item name="sortOrder" xsi:type="number">20</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="additionalClasses" xsi:type="string">store-pickup</item>
                                                                    </item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="store-fields" xsi:type="array">
                                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                                            <item name="displayArea" xsi:type="string">store-fields</item>
                                                                            <item name="children" xsi:type="array" />
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                                <item name="pickup" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/shipping/pickup</item>
                                                                    <item name="sortOrder" xsi:type="number">30</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="pickup-options" xsi:type="array">
                                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                                            <item name="displayArea" xsi:type="string">pickup-options</item>
                                                                            <item name="children" xsi:type="array" />
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                                <item name="shippit" xsi:type="array">
                                                                    <item name="sortOrder" xsi:type="number">30</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="shippit-options" xsi:type="array">
                                                                            <item name="children" xsi:type="array">
                                                                                <item name="shippit_authority_to_leave" xsi:type="array">
                                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/form/element/dependent</item>
                                                                                    <item name="config" xsi:type="array">
                                                                                        <item name="dependent" xsi:type="string"></item>
                                                                                    </item>
                                                                                </item>
                                                                                <item name="shippit_delivery_instructions" xsi:type="array">
                                                                                    <item name="validation" xsi:type="array">
                                                                                        <item name="validate-atl" xsi:type="boolean">true</item>
                                                                                    </item>
                                                                                </item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                                <item name="insider-rewards" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/insider-rewards</item>
                                                                    <item name="sortOrder" xsi:type="number">200</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="template" xsi:type="string">Totaltools_Checkout/view/payment/insider-rewards</item>
                                                                        <item name="deps" xsi:type="string">checkout.steps.shipping-step.shippingAddress.customer-email</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                        <item name="shipping-address-fieldset" xsi:type="array">
                                                            <item name="config" xsi:type="array">
                                                                <item name="label" xsi:type="string"></item>
                                                            </item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="is_business_address" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/form/element/dependent</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="dependent" xsi:type="string">company</item>
                                                                        <item name="label" xsi:type="string"></item>
                                                                        <item name="description" xsi:type="string">I'm delivering to a business</item>
                                                                    </item>
                                                                </item>
                                                                <item name="company" xsi:type="array">
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="label" xsi:type="string">Company (Optional)</item>
                                                                        <item name="validation" xsi:type="array">
                                                                            <item name="prevent-pobox-rule" xsi:type="boolean">false</item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                        <item name="customer-email" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="additional-login-email-fields" xsi:type="array">
                                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                                    <item name="displayArea" xsi:type="string">additional-login-email-fields</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="insider-rewards" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Totaltools_Checkout/js/view/insider-rewards</item>
                                                                            <item name="config" xsi:type="array">
                                                                                <item name="deps" xsi:type="string">checkout.steps.shipping-step.shippingAddress.customer-email</item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>

                                            </item>
                                        </item>
                                        <item name="billing-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="payment" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="additional-payment-validators" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="custom-validators" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/additional-validators</item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                                <item name="sidebar" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="continue-shopping" xsi:type="array">
                                            <item name="component" xsi:type="string">uiComponent</item>
                                            <item name="sortOrder" xsi:type="string">150</item>
                                            <item name="displayArea" xsi:type="string">after_summary</item>
                                            <item name="config" xsi:type="array">
                                                <item name="template" xsi:type="string">Totaltools_Checkout/continue_shopping</item>
                                            </item>
                                        </item>
                                        <item name="trust-seals" xsi:type="array">
                                            <item name="component" xsi:type="string">uiComponent</item>
                                            <item name="sortOrder" xsi:type="string">200</item>
                                            <item name="displayArea" xsi:type="string">after_summary</item>
                                            <item name="config" xsi:type="array">
                                                <item name="template" xsi:type="string">Totaltools_Checkout/seals</item>
                                            </item>
                                        </item>
                                        <item name="summary" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="giftCardAccount" xsi:type="array">
                                                    <item name="config" xsi:type="array">
                                                        <item name="componentDisabled" xsi:type="boolean">true</item>
                                                    </item>
                                                </item>
                                                <item name="cart_items" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="details" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="savings" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/cart-step/item/summary/details/yousave</item>
                                                                    <item name="displayArea" xsi:type="string">after_details</item>
                                                                    <item name="sortOrder" xsi:type="number">1</item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                                <item name="totals" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="subtotal" xsi:type="array">
                                                            <item name="config" xsi:type="array">
                                                                <item name="title" xsi:type="string" translate="true">Subtotal</item>
                                                            </item>
                                                        </item>
                                                        <item name="redeemed-rewards" xsi:type="array">
                                                            <item name="component" xsi:type="string">Magento_Reward/js/view/summary/reward</item>
                                                            <item name="sortOrder" xsi:type="number">300</item>
                                                            <item name="config" xsi:type="array">
                                                                <item name="template" xsi:type="string">Totaltools_Checkout/summary/redeemed-rewards</item>
                                                            </item>
                                                        </item>
                                                        <item name="total-savings" xsi:type="array">
                                                            <item name="component" xsi:type="string">Totaltools_Checkout/js/view/summary/savings</item>
                                                            <item name="sortOrder" xsi:type="number">250</item>
                                                            <item name="config" xsi:type="array">
                                                                <item name="title" xsi:type="string" translate="true">Savings</item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                    <item name="sortOrder" xsi:type="number">50</item>
                                                </item>
                                                <item name="insider-dollars" xsi:type="array">
                                                    <item name="component" xsi:type="string">Magento_Reward/js/view/payment/reward</item>
                                                    <item name="displayArea" xsi:type="string">after_summary</item>
                                                    <item name="sortOrder" xsi:type="number">75</item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="template" xsi:type="string">Totaltools_Checkout/summary/insider-dollars</item>
                                                    </item>
                                                </item>
                                                <item name="discount-giftcard" xsi:type="array">
                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/summary/discount-giftcard</item>
                                                    <item name="displayArea" xsi:type="string">after_summary</item>
                                                    <item name="sortOrder" xsi:type="number">100</item>
                                                </item>
                                                <item name="payment-methods" xsi:type="array">
                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/summary/payment-methods</item>
                                                    <item name="displayArea" xsi:type="string">after_summary</item>
                                                    <item name="sortOrder" xsi:type="number">150</item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="storelocator-header-wrapper" remove="true" />
        <referenceBlock name="top.search" remove="true" />
        <referenceBlock name="nav.bar" remove="true" />
        <referenceBlock name="cart.reminder" remove="true" />
    </body>
</page>