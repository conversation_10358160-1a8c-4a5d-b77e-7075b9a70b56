/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'mage/utils/wrapper',
    'uiRegistry',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/selected-shipping-method'
], function ($, wrapper, registry, quote, selectedShippingMethod) {
    'use strict';

    return function (processor) {
        return wrapper.wrap(processor, function (proceed, payload) {
            payload = proceed(payload);

            const checkoutProvider = registry?.get('checkoutProvider');
            const providerData = checkoutProvider?.get('shippingAddress') || {};

            const storelocatorId = providerData?.extension_attributes?.storelocator_id || quote.extension_attributes?.storelocator_id || 0;

            var extentionAttributes = {
                third_party_pickup: $('#pickup-options [name="third_party_pickup"]').is(':checked') ? 1 : 0,
                third_party_name: $('#pickup-options [name="third_party_name"]').val(),
                is_loyal: $('[name="is_loyal"]').is(':checked'),
                ...(storelocatorId ? { storelocator_id: storelocatorId } : {})
            };

            if (!selectedShippingMethod.isStorePickup()) {
                delete extentionAttributes.third_party_pickup;
                delete extentionAttributes.third_party_name;
                delete extentionAttributes.storelocator_id;
                delete payload.addressInformation?.shipping_address?.extensionAttributes?.storelocator_id;
            } else {
                delete payload.addressInformation.extension_attributes.shippit_authority_to_leave;
                delete payload.addressInformation.extension_attributes.shippit_delivery_instructions;
            }

            if ('undefined' != typeof payload.addressInformation?.billing_address?.extensionAttributes?.storelocator_id) {
                delete payload.addressInformation.billing_address.extensionAttributes.storelocator_id;
            }

            payload.addressInformation.extension_attributes = _.extend(
                payload.addressInformation.extension_attributes,
                extentionAttributes
            );
            
            payload.addressInformation.shipping_address.extensionAttributes = _.extend(
                payload.addressInformation.shipping_address.extensionAttributes,
                {
                    storelocator_id: extentionAttributes && extentionAttributes.storelocator_id ? extentionAttributes.storelocator_id : 0
                }
            );

            payload.addressInformation.custom_attributes = quote.shippingAddress().customAttributes;

            return payload;
        });
    };
});
