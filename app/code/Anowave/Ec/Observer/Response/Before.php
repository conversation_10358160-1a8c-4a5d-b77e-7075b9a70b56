<?php
/**
 * Anowave Magento 2 Google Tag Manager Enhanced Ecommerce (UA) Tracking
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Anowave license that is
 * available through the world-wide-web at this URL:
 * https://www.anowave.com/license-agreement/
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category 	Anowave
 * @package 	Anowave_Ec
 * @copyright 	Copyright (c) 2022 Anowave (https://www.anowave.com/)
 * @license  	https://www.anowave.com/license-agreement/
 */

namespace Anowave\Ec\Observer\Response;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;

class Before implements ObserverInterface
{
	/**
	 * @var \Anowave\Ec\Helper\Affiliation
	 */
	protected $affiliation;
	
	/**
	 * Constructor 
	 * 
	 * @param \Anowave\Ec\Helper\Affiliation $affiliation
	 */
	public function __construct
	(
		\Anowave\Ec\Helper\Affiliation $affiliation
	)
	{
		$this->affiliation = $affiliation;
	}
	
	/**
	 * Execute (non-PHPdoc)
	 *
	 * @see \Magento\Framework\Event\ObserverInterface::execute()
	 */
	public function execute(EventObserver $observer)
	{
		if ($this->affiliation->isEnabled())
		{
			/**
			 * Replace affiliate placeholders
			 */
			$body = str_replace
			(
				\Anowave\Ec\Helper\Affiliation::PLACEHOLDER, $this->affiliation->getAffiliation(), $observer->getResponse()->getBody()
			);
			
			/**
			 * Update response
			 */
			$observer->getResponse()->setBody($body);
		}
	}
}