<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Notification\MessageList">
        <arguments>
            <argument name="messages" xsi:type="array">
                <item name="ec" xsi:type="string">Anowave\Ec\Model\System\Message\Sticky</item>
                <item name="ec_integrity" xsi:type="string">Anowave\Ec\Model\System\Message\Integrity</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Service\OrderService">
        <plugin name="ec" type="Anowave\Ec\Plugin\OrderService" sortOrder="1" />
    </type>
    <type name="Magento\Framework\View\Element\Message\MessageConfigurationsPool">
        <arguments>
            <argument name="configurationsMap" xsi:type="array">
                <item name="addLogSuccessMessage" xsi:type="array">
                    <item name="renderer" xsi:type="const">\Magento\Framework\View\Element\Message\Renderer\BlockRenderer::CODE</item>
                    <item name="data" xsi:type="array">
                        <item name="template" xsi:type="string">Anowave_Ec::message.phtml</item>
                    </item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
	   <plugin name="ec" type="Anowave\Ec\Plugin\DataProviderCollection" sortOrder="1" />
	</type>
</config>