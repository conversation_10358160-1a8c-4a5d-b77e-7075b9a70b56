<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="admin_system_config_changed_section_ec">
        <observer name="ec" instance="Anowave\Ec\Observer\Config" />
    </event>
    <event name="sales_order_payment_refund">
    	<observer name="ec" instance="Anowave\Ec\Observer\Refund" />
    </event>
    <event name="order_cancel_after">
    	<observer name="ec" instance="Anowave\Ec\Observer\Order\Cancel\After" />
    </event>
</config>