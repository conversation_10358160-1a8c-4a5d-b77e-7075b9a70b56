<?php
/**
 * Anowave Magento 2 Google Tag Manager Enhanced Ecommerce (UA) Tracking
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Anowave license that is
 * available through the world-wide-web at this URL:
 * https://www.anowave.com/license-agreement/
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category 	Anowave
 * @package 	Anowave_Ec
 * @copyright 	Copyright (c) 2022 Anowave (https://www.anowave.com/)
 * @license  	https://www.anowave.com/license-agreement/
 */

namespace Anowave\Ec\Model\Serializer;

class Json
{
	/**
	 * {@inheritDoc}
	 * 
	 * @since 100.2.0
	 */
	public function serialize($data)
	{
		$result = json_encode($data);
		
		if (false === $result) 
		{
			throw new \InvalidArgumentException('Unable to serialize value.');
		}
		return $result;
	}
	
	/**
	 * {@inheritDoc}
	 * 
	 * @since 100.2.0
	 */
	public function unserialize($string)
	{
		$result = json_decode($string, true);
		
		if (json_last_error() !== JSON_ERROR_NONE) 
		{
			throw new \InvalidArgumentException('Unable to unserialize value.');
		}
		return $result;
	}
}
